// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://axvjyefklaqsmhfhwtyh.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF4dmp5ZWZrbGFxc21oZmh3dHloIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTI0MTU5MjUsImV4cCI6MjA2Nzk5MTkyNX0.zfHu8twjc0VSCl9I8-6UC8a9fWm-IPZukyVugQYYsnA";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});