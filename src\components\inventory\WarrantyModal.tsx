
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Award, Calendar, AlertTriangle, CheckCircle, Clock } from "lucide-react";

interface WarrantyModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedItem: any;
}

const WarrantyModal = ({ isOpen, onClose, selectedItem }: WarrantyModalProps) => {
  const [filterStatus, setFilterStatus] = useState("all");
  const [warranties, setWarranties] = useState([
    {
      id: 1,
      serialNumber: "AGM001",
      productName: "12V Deep Cycle AGM Battery",
      purchaseDate: "2024-01-15",
      warrantyStart: "2024-01-15",
      warrantyEnd: "2026-01-15",
      warrantyMonths: 24,
      customer: "John Smith",
      status: "Active",
      daysRemaining: 680,
      claimHistory: []
    },
    {
      id: 2,
      serialNumber: "CAR001",
      productName: "Car Battery 550CCA",
      purchaseDate: "2023-12-01",
      warrantyStart: "2023-12-01",
      warrantyEnd: "2024-12-01",
      warrantyMonths: 12,
      customer: "Sarah Johnson",
      status: "Expiring Soon",
      daysRemaining: 45,
      claimHistory: [
        { date: "2024-06-15", type: "Replacement", reason: "Manufacturing defect" }
      ]
    },
    {
      id: 3,
      serialNumber: "MAR001",
      productName: "Marine Battery 105Ah",
      purchaseDate: "2021-03-10",
      warrantyStart: "2021-03-10",
      warrantyEnd: "2024-03-10",
      warrantyMonths: 36,
      customer: "Mike Wilson",
      status: "Expired",
      daysRemaining: -30,
      claimHistory: []
    }
  ]);

  const getStatusInfo = (warranty) => {
    if (warranty.daysRemaining > 90) {
      return { status: "Active", color: "default", icon: CheckCircle };
    } else if (warranty.daysRemaining > 0) {
      return { status: "Expiring Soon", color: "destructive", icon: AlertTriangle };
    } else {
      return { status: "Expired", color: "secondary", icon: Clock };
    }
  };

  const filteredWarranties = warranties.filter(warranty => {
    if (filterStatus === "all") return true;
    const statusInfo = getStatusInfo(warranty);
    return statusInfo.status.toLowerCase().replace(" ", "-") === filterStatus;
  });

  const handleWarrantyClaim = (warrantyId: number) => {
    console.log(`Processing warranty claim for ID: ${warrantyId}`);
    // Here you would typically open a warranty claim form
  };

  const extendWarranty = (warrantyId: number) => {
    setWarranties(warranties.map(w => 
      w.id === warrantyId 
        ? { 
            ...w, 
            warrantyEnd: new Date(new Date(w.warrantyEnd).getTime() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            daysRemaining: w.daysRemaining + 365,
            warrantyMonths: w.warrantyMonths + 12
          }
        : w
    ));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Warranty Management
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-4 gap-4">
            <div className="p-4 bg-green-50 rounded-lg text-center">
              <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-green-600">
                {warranties.filter(w => w.daysRemaining > 90).length}
              </div>
              <div className="text-sm text-green-700">Active</div>
            </div>
            <div className="p-4 bg-orange-50 rounded-lg text-center">
              <AlertTriangle className="h-8 w-8 text-orange-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-orange-600">
                {warranties.filter(w => w.daysRemaining > 0 && w.daysRemaining <= 90).length}
              </div>
              <div className="text-sm text-orange-700">Expiring Soon</div>
            </div>
            <div className="p-4 bg-gray-50 rounded-lg text-center">
              <Clock className="h-8 w-8 text-gray-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-600">
                {warranties.filter(w => w.daysRemaining <= 0).length}
              </div>
              <div className="text-sm text-gray-700">Expired</div>
            </div>
            <div className="p-4 bg-blue-50 rounded-lg text-center">
              <Award className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-blue-600">
                {warranties.reduce((sum, w) => sum + w.claimHistory.length, 0)}
              </div>
              <div className="text-sm text-blue-700">Total Claims</div>
            </div>
          </div>

          {/* Filter */}
          <div>
            <Label htmlFor="status-filter">Filter by Status</Label>
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Warranties</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="expiring-soon">Expiring Soon</SelectItem>
                <SelectItem value="expired">Expired</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Warranties Table */}
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product & Serial</TableHead>
                  <TableHead>Customer</TableHead>
                  <TableHead>Warranty Period</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Claims</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredWarranties.map((warranty) => {
                  const statusInfo = getStatusInfo(warranty);
                  const StatusIcon = statusInfo.icon;
                  
                  return (
                    <TableRow key={warranty.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{warranty.productName}</p>
                          <p className="text-sm text-gray-500 font-mono">{warranty.serialNumber}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{warranty.customer}</p>
                          <p className="text-sm text-gray-500">Purchased: {warranty.purchaseDate}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="text-sm">
                            {warranty.warrantyStart} - {warranty.warrantyEnd}
                          </p>
                          <p className="text-sm text-gray-500">
                            {warranty.warrantyMonths} months coverage
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <StatusIcon className="h-4 w-4" />
                          <Badge variant={statusInfo.status === "Active" ? "default" : statusInfo.status === "Expiring Soon" ? "destructive" : "secondary"}>
                            {statusInfo.status}
                          </Badge>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          {warranty.daysRemaining > 0 
                            ? `${warranty.daysRemaining} days left`
                            : `Expired ${Math.abs(warranty.daysRemaining)} days ago`
                          }
                        </p>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="text-sm font-medium">
                            {warranty.claimHistory.length} claims
                          </p>
                          {warranty.claimHistory.length > 0 && (
                            <p className="text-xs text-gray-500">
                              Last: {warranty.claimHistory[warranty.claimHistory.length - 1].date}
                            </p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col gap-1">
                          {warranty.daysRemaining > 0 && (
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => handleWarrantyClaim(warranty.id)}
                            >
                              File Claim
                            </Button>
                          )}
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => extendWarranty(warranty.id)}
                          >
                            Extend
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>

          <div className="flex justify-end">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default WarrantyModal;
