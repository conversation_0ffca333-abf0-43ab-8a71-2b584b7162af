import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { POSHeader } from "@/components/pos/POSHeader";
import { CategoryFilter } from "@/components/pos/CategoryFilter";
import { ProductGrid } from "@/components/pos/ProductGrid";
import { CartPanel } from "@/components/pos/CartPanel";
import { CheckoutSection } from "@/components/pos/CheckoutSection";

interface CartItem {
  id: number;
  name: string;
  price: number;
  quantity: number;
  sku: string;
  barcode: string;
}

interface Customer {
  id: number;
  name: string;
  phone: string;
  email: string;
  discount: number;
}

interface HeldTransaction {
  id: string;
  cart: CartItem[];
  customer: Customer | null;
  discount: number;
  timestamp: Date;
}

const POS = () => {
  const [cart, setCart] = useState<CartItem[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [discountPercent, setDiscountPercent] = useState(0);
  const [paymentMethod, setPaymentMethod] = useState("cash");
  const [activeCategory, setActiveCategory] = useState("all");
  const [heldTransactions, setHeldTransactions] = useState<HeldTransaction[]>([]);
  const { toast } = useToast();

  const categories = [
    { id: "all", name: "All Products", color: "bg-blue-500" },
    { id: "automotive", name: "Automotive", color: "bg-red-500" },
    { id: "deep-cycle", name: "Deep Cycle", color: "bg-green-500" },
    { id: "marine", name: "Marine", color: "bg-blue-600" },
    { id: "motorcycle", name: "Motorcycle", color: "bg-orange-500" },
    { id: "industrial", name: "Industrial", color: "bg-purple-500" },
  ];

  const products = [
    { id: 1, name: "12V Deep Cycle AGM Battery", price: 129.99, sku: "AGM-12V-100", barcode: "123456789012", category: "deep-cycle", inStock: 15 },
    { id: 2, name: "Car Battery 550CCA", price: 89.99, sku: "CAR-550", barcode: "123456789013", category: "automotive", inStock: 8 },
    { id: 3, name: "Marine Battery 105Ah", price: 199.99, sku: "MAR-105", barcode: "123456789014", category: "marine", inStock: 12 },
    { id: 4, name: "Motorcycle Battery 12V", price: 45.99, sku: "MOTO-12V", barcode: "123456789015", category: "motorcycle", inStock: 20 },
    { id: 5, name: "Truck Battery 750CCA", price: 159.99, sku: "TRUCK-750", barcode: "123456789016", category: "automotive", inStock: 6 },
    { id: 6, name: "Golf Cart Battery 6V", price: 99.99, sku: "GOLF-6V", barcode: "123456789017", category: "deep-cycle", inStock: 10 },
    { id: 7, name: "Industrial Battery 24V", price: 299.99, sku: "IND-24V", barcode: "123456789018", category: "industrial", inStock: 4 },
    { id: 8, name: "Lawn Mower Battery", price: 39.99, sku: "LAWN-12V", barcode: "123456789019", category: "automotive", inStock: 25 },
  ];

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.sku.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.barcode.includes(searchQuery);
    const matchesCategory = activeCategory === "all" || product.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  const addToCart = (product: typeof products[0]) => {
    const existingItem = cart.find(item => item.id === product.id);
    if (existingItem) {
      setCart(cart.map(item =>
        item.id === product.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCart([...cart, {
        id: product.id,
        name: product.name,
        price: product.price,
        quantity: 1,
        sku: product.sku,
        barcode: product.barcode
      }]);
    }
  };

  const removeFromCart = (productId: number) => {
    setCart(cart.filter(item => item.id !== productId));
  };

  const updateQuantity = (productId: number, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(productId);
      return;
    }
    setCart(cart.map(item =>
      item.id === productId
        ? { ...item, quantity: newQuantity }
        : item
    ));
  };

  const handleProductScan = (scannedCode: string) => {
    const product = products.find(p => p.barcode === scannedCode || p.sku === scannedCode);
    if (product) {
      addToCart(product);
      toast({
        title: "Product Added",
        description: `${product.name} has been added to cart`,
      });
    } else {
      toast({
        title: "Product Not Found", 
        description: "No product found with this barcode/SKU",
        variant: "destructive"
      });
    }
  };

  const handleHoldTransaction = () => {
    if (cart.length === 0) {
      toast({
        title: "Empty Cart",
        description: "Cannot hold an empty transaction",
        variant: "destructive"
      });
      return;
    }

    const heldTransaction: HeldTransaction = {
      id: Date.now().toString(),
      cart: [...cart],
      customer: selectedCustomer,
      discount: discountPercent,
      timestamp: new Date()
    };

    setHeldTransactions([...heldTransactions, heldTransaction]);
    clearCart();
    toast({
      title: "Transaction Held",
      description: "Transaction has been saved and cart cleared",
    });
  };

  const handleGenerateQuote = () => {
    if (cart.length === 0) {
      toast({
        title: "Empty Cart",
        description: "Cannot generate quote for empty cart",
        variant: "destructive"
      });
      return;
    }

    const quoteNumber = `Q${Date.now()}`;
    toast({
      title: "Quote Generated",
      description: `Quote ${quoteNumber} has been generated and saved`,
    });
    
    console.log("Quote generated:", {
      quoteNumber,
      customer: selectedCustomer,
      items: cart,
      total: total,
      timestamp: new Date()
    });
  };

  const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const customerDiscount = selectedCustomer ? (subtotal * selectedCustomer.discount / 100) : 0;
  const manualDiscount = subtotal * discountPercent / 100;
  const totalDiscount = customerDiscount + manualDiscount;
  const discountedSubtotal = subtotal - totalDiscount;
  const tax = discountedSubtotal * 0.08;
  const total = discountedSubtotal + tax;

  const handleCheckout = () => {
    alert(`Processing ${paymentMethod} payment of $${total.toFixed(2)}`);
  };

  const clearCart = () => {
    setCart([]);
    setSelectedCustomer(null);
    setDiscountPercent(0);
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Left Panel - Products */}
      <div className="flex-1 flex flex-col bg-white">
        <POSHeader
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          selectedCustomer={selectedCustomer}
          onCustomerSelect={setSelectedCustomer}
          onProductScan={handleProductScan}
        />
        
        <CategoryFilter
          categories={categories}
          activeCategory={activeCategory}
          onCategoryChange={setActiveCategory}
        />
        
        <ProductGrid
          filteredProducts={filteredProducts}
          onAddToCart={addToCart}
        />
      </div>

      {/* Right Panel - Cart & Checkout */}
      <div className="w-96 bg-white border-l border-gray-200 flex flex-col">
        <CartPanel
          cart={cart}
          onUpdateQuantity={updateQuantity}
          onRemoveFromCart={removeFromCart}
          onClearCart={clearCart}
        />

        {cart.length > 0 && (
          <CheckoutSection
            subtotal={subtotal}
            customerDiscount={customerDiscount}
            manualDiscount={manualDiscount}
            tax={tax}
            total={total}
            discountPercent={discountPercent}
            setDiscountPercent={setDiscountPercent}
            selectedCustomer={selectedCustomer}
            paymentMethod={paymentMethod}
            setPaymentMethod={setPaymentMethod}
            onCheckout={handleCheckout}
            onHoldTransaction={handleHoldTransaction}
            onGenerateQuote={handleGenerateQuote}
          />
        )}
      </div>
    </div>
  );
};

export default POS;
