
import { useState } from "react";
import { TrendingUp, TrendingDown, DollarSign, Pie<PERSON>hart, BarChart3, Calendar, Download, Calculator } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { BarChart, Bar, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, <PERSON><PERSON><PERSON> as RechartsPie<PERSON>hart, Pie, Cell } from "recharts";
import { useToast } from "@/hooks/use-toast";

const FinancialReports = () => {
  const [dateRange, setDateRange] = useState("30d");
  const [reportType, setReportType] = useState("summary");
  const { toast } = useToast();

  // Sample financial data
  const profitLossData = [
    { month: 'Jan', revenue: 45000, cogs: 32000, grossProfit: 13000, expenses: 8000, netProfit: 5000 },
    { month: 'Feb', revenue: 52000, cogs: 37000, grossProfit: 15000, expenses: 8500, netProfit: 6500 },
    { month: 'Mar', revenue: 48000, cogs: 34000, grossProfit: 14000, expenses: 8200, netProfit: 5800 },
    { month: 'Apr', revenue: 55000, cogs: 39000, grossProfit: 16000, expenses: 8800, netProfit: 7200 },
    { month: 'May', revenue: 58000, cogs: 41000, grossProfit: 17000, expenses: 9000, netProfit: 8000 },
    { month: 'Jun', revenue: 62000, cogs: 44000, grossProfit: 18000, expenses: 9200, netProfit: 8800 },
  ];

  const cashFlowData = [
    { month: 'Jan', inflow: 45000, outflow: 40000, net: 5000, cumulative: 5000 },
    { month: 'Feb', inflow: 52000, outflow: 45500, net: 6500, cumulative: 11500 },
    { month: 'Mar', inflow: 48000, outflow: 42200, net: 5800, cumulative: 17300 },
    { month: 'Apr', inflow: 55000, outflow: 47800, net: 7200, cumulative: 24500 },
    { month: 'May', inflow: 58000, outflow: 50000, net: 8000, cumulative: 32500 },
    { month: 'Jun', inflow: 62000, outflow: 53200, net: 8800, cumulative: 41300 },
  ];

  const expenseBreakdown = [
    { category: 'Inventory', amount: 44000, percentage: 68, color: '#8884d8' },
    { category: 'Rent & Utilities', amount: 5500, percentage: 8.5, color: '#82ca9d' },
    { category: 'Salaries', amount: 8000, percentage: 12.4, color: '#ffc658' },
    { category: 'Marketing', amount: 2000, percentage: 3.1, color: '#ff7300' },
    { category: 'Other', amount: 5000, percentage: 7.7, color: '#00c49f' },
  ];

  const kpiData = {
    grossMargin: 29.2,
    netMargin: 14.2,
    roi: 18.5,
    currentRatio: 2.4,
    quickRatio: 1.8,
    inventoryTurnover: 6.2,
    receivablesTurnover: 12.5,
    payablesTurnover: 8.3,
  };

  const balanceSheetData = {
    assets: {
      current: {
        cash: 25000,
        accountsReceivable: 18000,
        inventory: 65000,
        prepaidExpenses: 3000,
      },
      fixed: {
        equipment: 45000,
        vehicles: 35000,
        property: 120000,
        accumulatedDepreciation: -25000,
      }
    },
    liabilities: {
      current: {
        accountsPayable: 22000,
        shortTermDebt: 8000,
        accruedExpenses: 5000,
      },
      longTerm: {
        longTermDebt: 85000,
        deferredTax: 3000,
      }
    },
    equity: {
      ownerEquity: 150000,
      retainedEarnings: 63000,
    }
  };

  const handleExportReport = (reportName: string) => {
    toast({
      title: "Export Started",
      description: `${reportName} is being generated and will be downloaded shortly.`,
    });
    
    setTimeout(() => {
      toast({
        title: "Export Complete",
        description: `${reportName} has been downloaded successfully.`,
      });
    }, 2000);
  };

  const calculateTotalAssets = () => {
    const currentAssets = Object.values(balanceSheetData.assets.current).reduce((sum, val) => sum + val, 0);
    const fixedAssets = Object.values(balanceSheetData.assets.fixed).reduce((sum, val) => sum + val, 0);
    return currentAssets + fixedAssets;
  };

  const calculateTotalLiabilities = () => {
    const currentLiab = Object.values(balanceSheetData.liabilities.current).reduce((sum, val) => sum + val, 0);
    const longTermLiab = Object.values(balanceSheetData.liabilities.longTerm).reduce((sum, val) => sum + val, 0);
    return currentLiab + longTermLiab;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Financial Reports</h1>
          <p className="text-gray-500">Comprehensive financial analysis and reporting</p>
        </div>
        <div className="flex gap-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={() => handleExportReport("Financial Summary")} className="bg-green-600 hover:bg-green-700">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Gross Margin</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpiData.grossMargin}%</div>
            <p className="text-xs text-muted-foreground">+2.1% from last month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Net Margin</CardTitle>
            <Calculator className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpiData.netMargin}%</div>
            <p className="text-xs text-muted-foreground">+1.5% from last month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">ROI</CardTitle>
            <DollarSign className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpiData.roi}%</div>
            <p className="text-xs text-muted-foreground">+3.2% from last month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Current Ratio</CardTitle>
            <BarChart3 className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{kpiData.currentRatio}</div>
            <p className="text-xs text-muted-foreground">Healthy liquidity</p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Reports */}
      <Tabs defaultValue="profit-loss" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="profit-loss">Profit & Loss</TabsTrigger>
          <TabsTrigger value="cash-flow">Cash Flow</TabsTrigger>
          <TabsTrigger value="balance-sheet">Balance Sheet</TabsTrigger>
          <TabsTrigger value="ratios">Financial Ratios</TabsTrigger>
        </TabsList>

        <TabsContent value="profit-loss" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Profit & Loss Trend
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => handleExportReport("P&L Statement")}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={profitLossData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line type="monotone" dataKey="revenue" stroke="#8884d8" name="Revenue" />
                    <Line type="monotone" dataKey="grossProfit" stroke="#82ca9d" name="Gross Profit" />
                    <Line type="monotone" dataKey="netProfit" stroke="#ffc658" name="Net Profit" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Expense Breakdown</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Pie
                      data={expenseBreakdown}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percentage }) => `${name} ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="amount"
                    >
                      {expenseBreakdown.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, 'Amount']} />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Detailed P&L Statement</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Month</TableHead>
                    <TableHead>Revenue</TableHead>
                    <TableHead>COGS</TableHead>
                    <TableHead>Gross Profit</TableHead>
                    <TableHead>Expenses</TableHead>
                    <TableHead>Net Profit</TableHead>
                    <TableHead>Margin %</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {profitLossData.map((row) => (
                    <TableRow key={row.month}>
                      <TableCell>{row.month}</TableCell>
                      <TableCell>${row.revenue.toLocaleString()}</TableCell>
                      <TableCell>${row.cogs.toLocaleString()}</TableCell>
                      <TableCell>${row.grossProfit.toLocaleString()}</TableCell>
                      <TableCell>${row.expenses.toLocaleString()}</TableCell>
                      <TableCell className="font-bold">${row.netProfit.toLocaleString()}</TableCell>
                      <TableCell>{((row.netProfit / row.revenue) * 100).toFixed(1)}%</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="cash-flow" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Cash Flow Analysis
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => handleExportReport("Cash Flow Statement")}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={cashFlowData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="inflow" fill="#82ca9d" name="Cash Inflow" />
                    <Bar dataKey="outflow" fill="#ff7300" name="Cash Outflow" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Cumulative Cash Flow</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={cashFlowData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Line type="monotone" dataKey="cumulative" stroke="#8884d8" strokeWidth={3} name="Cumulative Cash" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="balance-sheet" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  Assets
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => handleExportReport("Balance Sheet")}
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Current Assets</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Cash</span>
                      <span>${balanceSheetData.assets.current.cash.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Accounts Receivable</span>
                      <span>${balanceSheetData.assets.current.accountsReceivable.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Inventory</span>
                      <span>${balanceSheetData.assets.current.inventory.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Prepaid Expenses</span>
                      <span>${balanceSheetData.assets.current.prepaidExpenses.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Fixed Assets</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Equipment</span>
                      <span>${balanceSheetData.assets.fixed.equipment.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Vehicles</span>
                      <span>${balanceSheetData.assets.fixed.vehicles.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Property</span>
                      <span>${balanceSheetData.assets.fixed.property.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Accumulated Depreciation</span>
                      <span>${balanceSheetData.assets.fixed.accumulatedDepreciation.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
                <div className="border-t pt-2">
                  <div className="flex justify-between font-bold">
                    <span>Total Assets</span>
                    <span>${calculateTotalAssets().toLocaleString()}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Liabilities & Equity</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-semibold mb-2">Current Liabilities</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Accounts Payable</span>
                      <span>${balanceSheetData.liabilities.current.accountsPayable.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Short-term Debt</span>
                      <span>${balanceSheetData.liabilities.current.shortTermDebt.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Accrued Expenses</span>
                      <span>${balanceSheetData.liabilities.current.accruedExpenses.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Long-term Liabilities</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Long-term Debt</span>
                      <span>${balanceSheetData.liabilities.longTerm.longTermDebt.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Deferred Tax</span>
                      <span>${balanceSheetData.liabilities.longTerm.deferredTax.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">Owner's Equity</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Owner's Equity</span>
                      <span>${balanceSheetData.equity.ownerEquity.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Retained Earnings</span>
                      <span>${balanceSheetData.equity.retainedEarnings.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
                <div className="border-t pt-2">
                  <div className="flex justify-between font-bold">
                    <span>Total Liab. & Equity</span>
                    <span>${(calculateTotalLiabilities() + Object.values(balanceSheetData.equity).reduce((sum, val) => sum + val, 0)).toLocaleString()}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="ratios" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Liquidity Ratios</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Current Ratio</span>
                  <span className="font-bold">{kpiData.currentRatio}</span>
                </div>
                <div className="flex justify-between">
                  <span>Quick Ratio</span>
                  <span className="font-bold">{kpiData.quickRatio}</span>
                </div>
                <div className="text-xs text-muted-foreground">
                  Current ratio above 2.0 indicates good liquidity
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Activity Ratios</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Inventory Turnover</span>
                  <span className="font-bold">{kpiData.inventoryTurnover}x</span>
                </div>
                <div className="flex justify-between">
                  <span>Receivables Turnover</span>
                  <span className="font-bold">{kpiData.receivablesTurnover}x</span>
                </div>
                <div className="flex justify-between">
                  <span>Payables Turnover</span>
                  <span className="font-bold">{kpiData.payablesTurnover}x</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Profitability Ratios</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Gross Margin</span>
                  <span className="font-bold">{kpiData.grossMargin}%</span>
                </div>
                <div className="flex justify-between">
                  <span>Net Margin</span>
                  <span className="font-bold">{kpiData.netMargin}%</span>
                </div>
                <div className="flex justify-between">
                  <span>ROI</span>
                  <span className="font-bold">{kpiData.roi}%</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default FinancialReports;
