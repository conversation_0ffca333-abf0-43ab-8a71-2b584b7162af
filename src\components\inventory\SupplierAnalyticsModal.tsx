
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { TrendingUp, TrendingDown, Star, Truck, Clock, DollarSign, Package, Award } from "lucide-react";

interface SupplierAnalyticsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const SupplierAnalyticsModal = ({ isOpen, onClose }: SupplierAnalyticsModalProps) => {
  const [selectedPeriod, setSelectedPeriod] = useState("last-6-months");
  
  const [suppliers, setSuppliers] = useState([
    {
      id: "SUP001",
      name: "Battery World",
      rating: 4.5,
      totalOrders: 45,
      onTimeDelivery: 92,
      avgDeliveryTime: 12,
      totalSpent: 85750,
      qualityScore: 4.3,
      returnRate: 2.1,
      lastOrderDate: "2024-01-15",
      categories: ["Deep Cycle", "Marine"],
      contactPerson: "John Smith",
      email: "<EMAIL>",
      phone: "******-0123",
      performanceTrend: "up"
    },
    {
      id: "SUP002", 
      name: "AutoParts Inc",
      rating: 4.2,
      totalOrders: 32,
      onTimeDelivery: 87,
      avgDeliveryTime: 9,
      totalSpent: 62400,
      qualityScore: 4.1,
      returnRate: 3.2,
      lastOrderDate: "2024-01-10",
      categories: ["Automotive"],
      contactPerson: "Sarah Johnson",
      email: "<EMAIL>",
      phone: "******-0456",
      performanceTrend: "stable"
    },
    {
      id: "SUP003",
      name: "Marine Supply Co",
      rating: 4.7,
      totalOrders: 18,
      onTimeDelivery: 95,
      avgDeliveryTime: 14,
      totalSpent: 28900,
      qualityScore: 4.6,
      returnRate: 1.5,
      lastOrderDate: "2024-01-12",
      categories: ["Marine", "Deep Cycle"],
      contactPerson: "Mike Wilson",
      email: "<EMAIL>", 
      phone: "******-0789",
      performanceTrend: "up"
    }
  ]);

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return <TrendingUp className="h-4 w-4 text-green-500" />;
      case "down":
        return <TrendingDown className="h-4 w-4 text-red-500" />;
      default:
        return <div className="h-4 w-4 bg-gray-400 rounded-full" />;
    }
  };

  const getRatingStars = (rating: number) => {
    return [...Array(5)].map((_, i) => (
      <span key={i} className={`text-sm ${i < Math.floor(rating) ? 'text-yellow-500' : 'text-gray-300'}`}>★</span>
    ));
  };

  const getPerformanceColor = (score: number, threshold: number = 90) => {
    if (score >= threshold) return "text-green-600";
    if (score >= threshold - 10) return "text-yellow-600";
    return "text-red-600";
  };

  const totalSpent = suppliers.reduce((sum, supplier) => sum + supplier.totalSpent, 0);
  const avgRating = suppliers.reduce((sum, supplier) => sum + supplier.rating, 0) / suppliers.length;
  const avgOnTimeDelivery = suppliers.reduce((sum, supplier) => sum + supplier.onTimeDelivery, 0) / suppliers.length;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Supplier Performance Analytics
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Period Selection */}
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Performance Overview</h3>
            <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Select period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="last-month">Last Month</SelectItem>
                <SelectItem value="last-3-months">Last 3 Months</SelectItem>
                <SelectItem value="last-6-months">Last 6 Months</SelectItem>
                <SelectItem value="last-year">Last Year</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Summary Cards */}
          <div className="grid grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Suppliers</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{suppliers.length}</div>
                <p className="text-xs text-muted-foreground">Active suppliers</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Spent</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">${totalSpent.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">Last 6 months</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Rating</CardTitle>
                <Star className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{avgRating.toFixed(1)}</div>
                <div className="flex">{getRatingStars(avgRating)}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">On-Time Delivery</CardTitle>
                <Truck className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{avgOnTimeDelivery.toFixed(0)}%</div>
                <p className="text-xs text-muted-foreground">Average across all suppliers</p>
              </CardContent>
            </Card>
          </div>

          {/* Supplier Performance Table */}
          <Card>
            <CardHeader>
              <CardTitle>Detailed Supplier Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Supplier</TableHead>
                      <TableHead>Performance</TableHead>
                      <TableHead>Delivery Metrics</TableHead>
                      <TableHead>Financial</TableHead>
                      <TableHead>Quality</TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>Trend</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {suppliers.map((supplier) => (
                      <TableRow key={supplier.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{supplier.name}</p>
                            <p className="text-sm text-gray-500">{supplier.id}</p>
                            <div className="flex items-center gap-1 mt-1">
                              {getRatingStars(supplier.rating)}
                              <span className="text-sm text-gray-500 ml-1">({supplier.rating})</span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <span className="text-sm">Orders:</span>
                              <Badge variant="outline">{supplier.totalOrders}</Badge>
                            </div>
                            <div className="text-sm text-gray-500">
                              Categories: {supplier.categories.join(", ")}
                            </div>
                            <div className="text-xs text-gray-400">
                              Last order: {supplier.lastOrderDate}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <Clock className="h-3 w-3" />
                              <span className="text-sm">{supplier.avgDeliveryTime} days avg</span>
                            </div>
                            <div className={`text-sm font-medium ${getPerformanceColor(supplier.onTimeDelivery)}`}>
                              {supplier.onTimeDelivery}% on-time
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div 
                                className={`h-2 rounded-full ${supplier.onTimeDelivery >= 90 ? 'bg-green-500' : supplier.onTimeDelivery >= 80 ? 'bg-yellow-500' : 'bg-red-500'}`}
                                style={{ width: `${supplier.onTimeDelivery}%` }}
                              ></div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <p className="font-medium">${supplier.totalSpent.toLocaleString()}</p>
                            <p className="text-sm text-gray-500">
                              {((supplier.totalSpent / totalSpent) * 100).toFixed(1)}% of total
                            </p>
                            <div className="w-20 bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-blue-500 h-2 rounded-full"
                                style={{ width: `${(supplier.totalSpent / totalSpent) * 100}%` }}
                              ></div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center gap-1">
                              <Award className="h-3 w-3" />
                              <span className="text-sm">{supplier.qualityScore}/5.0</span>
                            </div>
                            <p className="text-sm text-gray-500">
                              {supplier.returnRate}% return rate
                            </p>
                            <Badge 
                              variant={supplier.returnRate < 2 ? "default" : supplier.returnRate < 5 ? "secondary" : "destructive"}
                              className="text-xs"
                            >
                              {supplier.returnRate < 2 ? "Excellent" : supplier.returnRate < 5 ? "Good" : "Needs Improvement"}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <p className="text-sm font-medium">{supplier.contactPerson}</p>
                            <p className="text-xs text-gray-500">{supplier.email}</p>
                            <p className="text-xs text-gray-500">{supplier.phone}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {getTrendIcon(supplier.performanceTrend)}
                            <span className="text-sm capitalize">{supplier.performanceTrend}</span>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-between">
            <div className="flex gap-2">
              <Button variant="outline">
                Export Report
              </Button>
              <Button variant="outline">
                Send Performance Review
              </Button>
            </div>
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SupplierAnalyticsModal;
