import React, { createContext, useContext, ReactNode } from 'react';
import { BusinessTerminology } from '@/types/business';
import { useBusinessConfig } from '@/hooks/useBusinessConfig';

interface TerminologyContextType {
  terminology: BusinessTerminology;
  t: (key: string, fallback?: string) => string;
}

const TerminologyContext = createContext<TerminologyContextType | undefined>(undefined);

interface TerminologyProviderProps {
  children: ReactNode;
}

export const TerminologyProvider = ({ children }: TerminologyProviderProps) => {
  const { terminology } = useBusinessConfig();

  // Helper function to get terminology values with dot notation
  const t = (key: string, fallback?: string): string => {
    const keys = key.split('.');
    let value: any = terminology;
    
    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k];
      } else {
        return fallback || key;
      }
    }
    
    return typeof value === 'string' ? value : fallback || key;
  };

  return (
    <TerminologyContext.Provider value={{ terminology, t }}>
      {children}
    </TerminologyContext.Provider>
  );
};

export const useTerminology = (): TerminologyContextType => {
  const context = useContext(TerminologyContext);
  if (context === undefined) {
    throw new Error('useTerminology must be used within a TerminologyProvider');
  }
  return context;
};

// Convenience hooks for common terminology
export const useProductTerminology = () => {
  const { t } = useTerminology();
  return {
    singular: t('product.singular', 'Product'),
    plural: t('product.plural', 'Products'),
    singularLower: t('product.singular', 'Product').toLowerCase(),
    pluralLower: t('product.plural', 'Products').toLowerCase()
  };
};

export const useInventoryTerminology = () => {
  const { t } = useTerminology();
  return {
    stock: t('inventory.stock', 'Stock'),
    warehouse: t('inventory.warehouse', 'Warehouse'),
    category: t('inventory.category', 'Category'),
    stockLower: t('inventory.stock', 'Stock').toLowerCase(),
    warehouseLower: t('inventory.warehouse', 'Warehouse').toLowerCase(),
    categoryLower: t('inventory.category', 'Category').toLowerCase()
  };
};

export const useSalesTerminology = () => {
  const { t } = useTerminology();
  return {
    customer: t('sales.customer', 'Customer'),
    order: t('sales.order', 'Order'),
    orderPlural: t('sales.orderPlural', 'Orders'),
    receipt: t('sales.receipt', 'Receipt'),
    transaction: t('sales.transaction', 'Sale'),
    transactionPlural: t('sales.transactionPlural', 'Sales'),
    customerLower: t('sales.customer', 'Customer').toLowerCase(),
    orderLower: t('sales.order', 'Order').toLowerCase(),
    receiptLower: t('sales.receipt', 'Receipt').toLowerCase()
  };
};

export const useCustomTerminology = (key: string, fallback?: string) => {
  const { t } = useTerminology();
  return t(`custom.${key}`, fallback);
};
