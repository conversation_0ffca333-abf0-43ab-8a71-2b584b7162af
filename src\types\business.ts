// Business Configuration Types

export interface BusinessType {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
}

export interface IndustryTemplate {
  id: string;
  name: string;
  businessType: string;
  description: string;
  terminology: BusinessTerminology;
  defaultCategories: CategoryTemplate[];
  defaultAttributes: AttributeTemplate[];
}

export interface BusinessTerminology {
  // Core product terminology
  product: {
    singular: string; // "Product", "Item", "Battery", "Garment", etc.
    plural: string;   // "Products", "Items", "Batteries", "Garments", etc.
    singularLower?: string;
    pluralLower?: string;
  };

  // Inventory terminology
  inventory: {
    stock: string;    // "Stock", "Inventory", "Units", etc.
    warehouse: string; // "Warehouse", "Store", "Location", etc.
    category: string;  // "Category", "Type", "Department", etc.
    stockLower?: string;
    warehouseLower?: string;
    categoryLower?: string;
  };

  // Sales terminology
  sales: {
    customer: string;  // "Customer", "Client", "Patient", etc.
    order: string;     // "Order", "Sale", "Transaction", etc.
    orderPlural?: string; // "Orders", "Sales", "Transactions", etc.
    receipt: string;   // "Receipt", "Invoice", "Ticket", etc.
    transaction?: string; // "Sale", "Transaction", "Purchase", etc.
    transactionPlural?: string; // "Sales", "Transactions", "Purchases", etc.
    customerLower?: string;
    orderLower?: string;
    receiptLower?: string;
  };

  // Additional industry-specific terms
  custom: Record<string, string>;
}

export interface CategoryTemplate {
  name: string;
  description?: string;
  color: string;
  icon?: string;
  parentName?: string;
  sortOrder: number;
}

export interface AttributeTemplate {
  name: string;
  label: string;
  type: AttributeType;
  options?: string[];
  isRequired: boolean;
  sortOrder: number;
}

export type AttributeType = 
  | 'text' 
  | 'number' 
  | 'select' 
  | 'multiselect' 
  | 'boolean' 
  | 'date' 
  | 'textarea' 
  | 'url' 
  | 'email';

export interface BusinessConfiguration {
  id: string;
  businessName: string;
  businessType: string;
  industryTemplate: string;
  terminology: BusinessTerminology;
  createdAt: string;
  updatedAt: string;
}

export interface ProductCategory {
  id: string;
  businessId: string;
  name: string;
  description?: string;
  color: string;
  icon?: string;
  parentId?: string;
  sortOrder: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ProductAttribute {
  id: string;
  businessId: string;
  name: string;
  label: string;
  type: AttributeType;
  options?: string[];
  isRequired: boolean;
  sortOrder: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface FlexibleProduct {
  id: string;
  businessId: string;
  name: string;
  sku: string;
  barcode?: string;
  categoryId: string;
  attributes: Record<string, any>;
  price: number;
  cost: number;
  stockQuantity: number;
  minStock: number;
  maxStock?: number;
  reorderPoint: number;
  supplierId?: string;
  location?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Predefined Business Types
export const BUSINESS_TYPES: BusinessType[] = [
  {
    id: 'electronics',
    name: 'Electronics',
    description: 'Consumer electronics, components, and tech accessories',
    icon: 'Smartphone',
    color: 'bg-blue-500'
  },
  {
    id: 'automotive',
    name: 'Automotive',
    description: 'Auto parts, batteries, and automotive accessories',
    icon: 'Car',
    color: 'bg-red-500'
  },
  {
    id: 'clothing',
    name: 'Clothing & Fashion',
    description: 'Apparel, accessories, and fashion items',
    icon: 'Shirt',
    color: 'bg-purple-500'
  },
  {
    id: 'food-beverage',
    name: 'Food & Beverage',
    description: 'Food products, beverages, and consumables',
    icon: 'Coffee',
    color: 'bg-orange-500'
  },
  {
    id: 'health-beauty',
    name: 'Health & Beauty',
    description: 'Cosmetics, health products, and wellness items',
    icon: 'Heart',
    color: 'bg-pink-500'
  },
  {
    id: 'home-garden',
    name: 'Home & Garden',
    description: 'Home improvement, furniture, and garden supplies',
    icon: 'Home',
    color: 'bg-green-500'
  },
  {
    id: 'sports-outdoors',
    name: 'Sports & Outdoors',
    description: 'Sporting goods, outdoor equipment, and fitness items',
    icon: 'Dumbbell',
    color: 'bg-indigo-500'
  },
  {
    id: 'books-media',
    name: 'Books & Media',
    description: 'Books, magazines, music, and digital media',
    icon: 'Book',
    color: 'bg-yellow-500'
  },
  {
    id: 'toys-games',
    name: 'Toys & Games',
    description: 'Toys, games, and entertainment products',
    icon: 'Gamepad2',
    color: 'bg-cyan-500'
  },
  {
    id: 'office-supplies',
    name: 'Office Supplies',
    description: 'Stationery, office equipment, and business supplies',
    icon: 'Briefcase',
    color: 'bg-gray-500'
  },
  {
    id: 'general-retail',
    name: 'General Retail',
    description: 'Mixed retail products and general merchandise',
    icon: 'Store',
    color: 'bg-slate-500'
  }
];
