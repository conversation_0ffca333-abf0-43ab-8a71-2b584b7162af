
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Package, Barcode } from "lucide-react";

interface StockReceivingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onReceive: (data: any) => void;
}

const StockReceivingModal = ({ isOpen, onClose, onReceive }: StockReceivingModalProps) => {
  const [receivingData, setReceivingData] = useState({
    productId: "",
    quantity: "",
    batchNumber: "",
    expiryDate: "",
    supplierId: "",
    location: "",
    unitCost: "",
    generateBarcode: false
  });

  const suppliers = [
    { id: "1", name: "Battery World" },
    { id: "2", name: "AutoParts Inc" },
    { id: "3", name: "Marine Supply Co" }
  ];

  const locations = [
    { id: "A1-B2", name: "Warehouse A - Section 1" },
    { id: "B1-A3", name: "Warehouse B - Section 1" },
    { id: "C2-B1", name: "Warehouse C - Section 2" }
  ];

  const generateBarcodeNumber = () => {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `BAT${timestamp.slice(-6)}${random}`;
  };

  const handleSubmit = () => {
    const barcode = receivingData.generateBarcode ? generateBarcodeNumber() : "";
    onReceive({
      ...receivingData,
      barcode,
      receivedDate: new Date().toISOString()
    });
    onClose();
    setReceivingData({
      productId: "",
      quantity: "",
      batchNumber: "",
      expiryDate: "",
      supplierId: "",
      location: "",
      unitCost: "",
      generateBarcode: false
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Receive Stock
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="quantity">Quantity Received</Label>
            <Input
              id="quantity"
              type="number"
              value={receivingData.quantity}
              onChange={(e) => setReceivingData({...receivingData, quantity: e.target.value})}
              placeholder="Enter quantity"
            />
          </div>

          <div>
            <Label htmlFor="batchNumber">Batch/Lot Number</Label>
            <Input
              id="batchNumber"
              value={receivingData.batchNumber}
              onChange={(e) => setReceivingData({...receivingData, batchNumber: e.target.value})}
              placeholder="Enter batch number"
            />
          </div>

          <div>
            <Label htmlFor="expiryDate">Warranty Expiry Date</Label>
            <Input
              id="expiryDate"
              type="date"
              value={receivingData.expiryDate}
              onChange={(e) => setReceivingData({...receivingData, expiryDate: e.target.value})}
            />
          </div>

          <div>
            <Label htmlFor="supplier">Supplier</Label>
            <Select value={receivingData.supplierId} onValueChange={(value) => setReceivingData({...receivingData, supplierId: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Select supplier" />
              </SelectTrigger>
              <SelectContent>
                {suppliers.map((supplier) => (
                  <SelectItem key={supplier.id} value={supplier.id}>
                    {supplier.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="location">Storage Location</Label>
            <Select value={receivingData.location} onValueChange={(value) => setReceivingData({...receivingData, location: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Select location" />
              </SelectTrigger>
              <SelectContent>
                {locations.map((location) => (
                  <SelectItem key={location.id} value={location.id}>
                    {location.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="unitCost">Unit Cost</Label>
            <Input
              id="unitCost"
              type="number"
              step="0.01"
              value={receivingData.unitCost}
              onChange={(e) => setReceivingData({...receivingData, unitCost: e.target.value})}
              placeholder="Enter unit cost"
            />
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="generateBarcode"
              checked={receivingData.generateBarcode}
              onChange={(e) => setReceivingData({...receivingData, generateBarcode: e.target.checked})}
              className="rounded"
            />
            <Label htmlFor="generateBarcode" className="flex items-center gap-2">
              <Barcode className="h-4 w-4" />
              Generate Barcode
            </Label>
          </div>

          <div className="flex gap-2 pt-4">
            <Button variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
            <Button onClick={handleSubmit} className="flex-1">
              Receive Stock
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default StockReceivingModal;
