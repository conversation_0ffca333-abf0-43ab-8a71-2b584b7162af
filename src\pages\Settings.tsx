
import { useState } from "react";
import { Settings as SettingsIcon, Save, Download, Upload, RefreshCw, Shield, Bell, CreditCard, Database, Users, Package } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import SettingsModal from "@/components/SettingsModal";

interface SettingsData {
  businessName: string;
  address: string;
  phone: string;
  email: string;
  taxRate: number;
  currency: string;
  timezone: string;
  lowStockThreshold: number;
  autoReorder: boolean;
  emailAlerts: boolean;
  smsAlerts: boolean;
  backupFrequency: string;
}

const Settings = () => {
  const { toast } = useToast();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [settings, setSettings] = useState<SettingsData>({
    businessName: "BatteryHub Pro",
    address: "123 Battery Street, Power City, PC 12345",
    phone: "(*************",
    email: "<EMAIL>",
    taxRate: 8.25,
    currency: "USD",
    timezone: "America/New_York",
    lowStockThreshold: 5,
    autoReorder: false,
    emailAlerts: true,
    smsAlerts: false,
    backupFrequency: "daily"
  });

  const handleSaveSettings = (newSettings: SettingsData) => {
    setSettings(newSettings);
  };

  const handleExportData = () => {
    toast({
      title: "Export Started",
      description: "Your data is being exported. This may take a few minutes.",
    });
    
    setTimeout(() => {
      toast({
        title: "Export Complete",
        description: "Data export has been downloaded to your device.",
      });
    }, 3000);
  };

  const handleImportData = () => {
    toast({
      title: "Import Ready",
      description: "Please select a file to import your data.",
    });
  };

  const handleBackupNow = () => {
    toast({
      title: "Backup Started",
      description: "Creating system backup. You'll be notified when complete.",
    });
    
    setTimeout(() => {
      toast({
        title: "Backup Complete",
        description: "System backup created successfully.",
      });
    }, 2000);
  };

  const handleTestNotifications = () => {
    toast({
      title: "Test Notification",
      description: "This is a test notification from your system.",
    });
  };

  const systemStats = {
    totalUsers: 3,
    totalProducts: 156,
    totalCustomers: 234,
    systemUptime: "99.9%",
    lastBackup: "2024-01-20 03:00 AM",
    storageUsed: "2.3 GB",
    storageLimit: "10 GB"
  };

  const integrations = [
    { name: "QuickBooks", status: "connected", type: "accounting" },
    { name: "Stripe", status: "connected", type: "payment" },
    { name: "Mailchimp", status: "disconnected", type: "marketing" },
    { name: "Slack", status: "connected", type: "communication" },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">System Settings</h1>
          <p className="text-gray-500">Configure your battery management system</p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={handleBackupNow} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Backup Now
          </Button>
          <Button onClick={() => setIsModalOpen(true)} className="bg-blue-600 hover:bg-blue-700">
            <SettingsIcon className="h-4 w-4 mr-2" />
            Edit Settings
          </Button>
        </div>
      </div>

      <Tabs defaultValue="general" className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="inventory">Inventory</TabsTrigger>
          <TabsTrigger value="payments">Payments</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Business Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="text-sm font-medium text-gray-500">Business Name</p>
                  <p className="text-lg">{settings.businessName}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Address</p>
                  <p>{settings.address}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Contact</p>
                  <p>{settings.phone}</p>
                  <p>{settings.email}</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>System Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Users:</span>
                  <span className="font-medium">{systemStats.totalUsers}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Products:</span>
                  <span className="font-medium">{systemStats.totalProducts}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Customers:</span>
                  <span className="font-medium">{systemStats.totalCustomers}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">System Uptime:</span>
                  <Badge variant="default">{systemStats.systemUptime}</Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Data Management</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm text-gray-600 mb-2">Export your data for backup or migration</p>
                  <Button onClick={handleExportData} variant="outline" className="w-full">
                    <Download className="h-4 w-4 mr-2" />
                    Export Data
                  </Button>
                </div>
                <div>
                  <p className="text-sm text-gray-600 mb-2">Import data from another system</p>
                  <Button onClick={handleImportData} variant="outline" className="w-full">
                    <Upload className="h-4 w-4 mr-2" />
                    Import Data
                  </Button>
                </div>
                <div>
                  <p className="text-sm text-gray-600 mb-2">Last backup: {systemStats.lastBackup}</p>
                  <Button onClick={handleBackupNow} variant="outline" className="w-full">
                    <Database className="h-4 w-4 mr-2" />
                    Create Backup
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="inventory" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Inventory Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <p className="text-sm font-medium text-gray-500 mb-2">Low Stock Threshold</p>
                  <p className="text-lg">{settings.lowStockThreshold} units</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 mb-2">Auto-Reorder</p>
                  <Badge variant={settings.autoReorder ? "default" : "secondary"}>
                    {settings.autoReorder ? "Enabled" : "Disabled"}
                  </Badge>
                </div>
              </div>
              <div className="pt-4">
                <Button onClick={() => setIsModalOpen(true)} variant="outline">
                  <Package className="h-4 w-4 mr-2" />
                  Configure Inventory Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Payment Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <p className="text-sm font-medium text-gray-500 mb-2">Tax Rate</p>
                  <p className="text-lg">{settings.taxRate}%</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 mb-2">Currency</p>
                  <p className="text-lg">{settings.currency}</p>
                </div>
              </div>
              <div className="pt-4">
                <Button onClick={() => setIsModalOpen(true)} variant="outline">
                  <CreditCard className="h-4 w-4 mr-2" />
                  Configure Payment Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Email Alerts</p>
                    <p className="text-sm text-gray-500">Receive alerts via email</p>
                  </div>
                  <Badge variant={settings.emailAlerts ? "default" : "secondary"}>
                    {settings.emailAlerts ? "Enabled" : "Disabled"}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">SMS Alerts</p>
                    <p className="text-sm text-gray-500">Receive alerts via SMS</p>
                  </div>
                  <Badge variant={settings.smsAlerts ? "default" : "secondary"}>
                    {settings.smsAlerts ? "Enabled" : "Disabled"}
                  </Badge>
                </div>
              </div>
              <div className="flex space-x-2 pt-4">
                <Button onClick={() => setIsModalOpen(true)} variant="outline">
                  <Bell className="h-4 w-4 mr-2" />
                  Configure Notifications
                </Button>
                <Button onClick={handleTestNotifications} variant="outline">
                  Test Notification
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <p className="text-sm font-medium text-gray-500 mb-2">Last Password Change</p>
                  <p>30 days ago</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 mb-2">Two-Factor Authentication</p>
                  <Badge variant="secondary">Not Enabled</Badge>
                </div>
              </div>
              <div className="pt-4">
                <Button variant="outline">
                  <Shield className="h-4 w-4 mr-2" />
                  Security Settings
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="integrations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Third-Party Integrations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {integrations.map((integration, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <p className="font-medium">{integration.name}</p>
                      <p className="text-sm text-gray-500 capitalize">{integration.type}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant={integration.status === "connected" ? "default" : "secondary"}>
                        {integration.status}
                      </Badge>
                      <Button size="sm" variant="outline">
                        {integration.status === "connected" ? "Configure" : "Connect"}
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <SettingsModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        settings={settings}
        onSave={handleSaveSettings}
      />
    </div>
  );
};

export default Settings;
