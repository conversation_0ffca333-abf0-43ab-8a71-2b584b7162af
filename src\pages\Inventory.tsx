
import { useState, useEffect } from "react";
import { Package, Search, Plus, Edit, AlertTriangle, Battery, Truck, ArrowRightLeft, FileText, Barcode, AlertCircle } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import StockReceivingModal from "@/components/inventory/StockReceivingModal";
import StockTransferModal from "@/components/inventory/StockTransferModal";
import PurchaseOrderModal from "@/components/inventory/PurchaseOrderModal";

const Inventory = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [locationFilter, setLocationFilter] = useState("all");
  const [showReceivingModal, setShowReceivingModal] = useState(false);
  const [showTransferModal, setShowTransferModal] = useState(false);
  const [showPOModal, setShowPOModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);
  const [notifications, setNotifications] = useState([]);

  // Enhanced inventory data with all required fields
  const [inventory, setInventory] = useState([
    {
      id: 1,
      name: "12V Deep Cycle AGM Battery",
      sku: "AGM-12V-100",
      barcode: "BAT001AGM100",
      category: "Deep Cycle",
      currentStock: 15,
      minStock: 5,
      maxStock: 25,
      reorderPoint: 8,
      unitPrice: 129.99,
      unitCost: 95.00,
      supplier: "Battery World",
      supplierId: "1",
      lastRestocked: "2024-01-15",
      location: "A1-B2",
      batchNumber: "BCH-2024-001",
      expiryDate: "2026-01-15",
      warrantyMonths: 24,
      specifications: {
        voltage: "12V",
        capacity: "100Ah",
        dimensions: "330x175x220mm",
        weight: "28kg"
      }
    },
    {
      id: 2,
      name: "Car Battery 550CCA",
      sku: "CAR-550",
      barcode: "BAT002CAR550",
      category: "Automotive",
      currentStock: 2,
      minStock: 5,
      maxStock: 20,
      reorderPoint: 7,
      unitPrice: 89.99,
      unitCost: 65.00,
      supplier: "AutoParts Inc",
      supplierId: "2",
      lastRestocked: "2024-01-10",
      location: "B1-A3",
      batchNumber: "BCH-2024-002",
      expiryDate: "2025-12-10",
      warrantyMonths: 12,
      specifications: {
        voltage: "12V",
        cca: "550",
        dimensions: "242x175x190mm",
        weight: "18kg"
      }
    },
    {
      id: 3,
      name: "Marine Battery 105Ah",
      sku: "MAR-105",
      barcode: "BAT003MAR105",
      category: "Marine",
      currentStock: 12,
      minStock: 3,
      maxStock: 15,
      reorderPoint: 5,
      unitPrice: 199.99,
      unitCost: 145.00,
      supplier: "Marine Supply Co",
      supplierId: "3",
      lastRestocked: "2024-01-12",
      location: "C2-B1",
      batchNumber: "BCH-2024-003",
      expiryDate: "2026-06-12",
      warrantyMonths: 36,
      specifications: {
        voltage: "12V",
        capacity: "105Ah",
        dimensions: "330x175x240mm",
        weight: "32kg"
      }
    }
  ]);

  const categories = ["all", "Deep Cycle", "Automotive", "Marine", "Motorcycle", "Heavy Duty"];
  const locations = ["all", "A1-B2", "B1-A3", "C2-B1", "D1-A2"];

  // Check for low stock and reorder notifications
  useEffect(() => {
    const lowStockItems = inventory.filter(item => item.currentStock <= item.minStock);
    const reorderItems = inventory.filter(item => item.currentStock <= item.reorderPoint);
    const expiringItems = inventory.filter(item => {
      const expiryDate = new Date(item.expiryDate);
      const sixMonthsFromNow = new Date();
      sixMonthsFromNow.setMonth(sixMonthsFromNow.getMonth() + 6);
      return expiryDate <= sixMonthsFromNow;
    });

    const newNotifications = [
      ...lowStockItems.map(item => ({
        id: `low-${item.id}`,
        type: 'low-stock',
        message: `${item.name} is low on stock (${item.currentStock} remaining)`,
        severity: 'high'
      })),
      ...reorderItems.map(item => ({
        id: `reorder-${item.id}`,
        type: 'reorder',
        message: `${item.name} needs reordering (below reorder point)`,
        severity: 'medium'
      })),
      ...expiringItems.map(item => ({
        id: `expiry-${item.id}`,
        type: 'expiry',
        message: `${item.name} warranty expires soon (${item.expiryDate})`,
        severity: 'medium'
      }))
    ];

    setNotifications(newNotifications);
  }, [inventory]);

  const filteredInventory = inventory.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.sku.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.barcode.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = categoryFilter === "all" || item.category === categoryFilter;
    const matchesLocation = locationFilter === "all" || item.location === locationFilter;
    return matchesSearch && matchesCategory && matchesLocation;
  });

  const getStockStatus = (current: number, min: number, reorderPoint: number) => {
    if (current <= min) return { status: "critical", color: "destructive", text: "Critical" };
    if (current <= reorderPoint) return { status: "low", color: "destructive", text: "Low" };
    if (current <= min * 2) return { status: "medium", color: "default", text: "Medium" };
    return { status: "good", color: "default", text: "Good" };
  };

  const handleReceiveStock = (data: any) => {
    console.log("Receiving stock:", data);
    // Here you would typically update the inventory in your database
    // For now, we'll just log the data
  };

  const handleTransferStock = (data: any) => {
    console.log("Transferring stock:", data);
    // Here you would typically update the inventory in your database
  };

  const handleCreatePO = (data: any) => {
    console.log("Creating purchase order:", data);
    // Here you would typically save the PO to your database
  };

  const openTransferModal = (item: any) => {
    setSelectedItem(item);
    setShowTransferModal(true);
  };

  const totalValue = inventory.reduce((sum, item) => sum + (item.currentStock * item.unitCost), 0);
  const lowStockCount = inventory.filter(item => item.currentStock <= item.minStock).length;
  const reorderCount = inventory.filter(item => item.currentStock <= item.reorderPoint).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Inventory Management</h1>
          <p className="text-gray-500">Track and manage your battery inventory with real-time updates</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setShowReceivingModal(true)} className="bg-green-600 hover:bg-green-700">
            <Truck className="h-4 w-4 mr-2" />
            Receive Stock
          </Button>
          <Button onClick={() => setShowPOModal(true)} variant="outline">
            <FileText className="h-4 w-4 mr-2" />
            Create PO
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Plus className="h-4 w-4 mr-2" />
            Add Product
          </Button>
        </div>
      </div>

      {/* Notifications */}
      {notifications.length > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader className="pb-3">
            <CardTitle className="text-orange-800 flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              Notifications ({notifications.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {notifications.slice(0, 5).map((notification) => (
                <div key={notification.id} className="flex items-center gap-2 text-sm">
                  <div className={`w-2 h-2 rounded-full ${notification.severity === 'high' ? 'bg-red-500' : 'bg-yellow-500'}`} />
                  <span className="text-orange-800">{notification.message}</span>
                </div>
              ))}
              {notifications.length > 5 && (
                <p className="text-sm text-orange-600 mt-2">+{notifications.length - 5} more notifications</p>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Products</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inventory.length}</div>
            <p className="text-xs text-muted-foreground">Active inventory items</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Low Stock Alerts</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{lowStockCount}</div>
            <p className="text-xs text-muted-foreground">Items below minimum stock</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Reorder Needed</CardTitle>
            <Package className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{reorderCount}</div>
            <p className="text-xs text-muted-foreground">Items need reordering</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <Battery className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalValue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Current inventory value (cost)</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
          <Input
            placeholder="Search by product name, SKU, or barcode..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={categoryFilter} onValueChange={setCategoryFilter}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="Filter by category" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category} value={category}>
                {category === "all" ? "All Categories" : category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={locationFilter} onValueChange={setLocationFilter}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="Filter by location" />
          </SelectTrigger>
          <SelectContent>
            {locations.map((location) => (
              <SelectItem key={location} value={location}>
                {location === "all" ? "All Locations" : location}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Enhanced Inventory Table */}
      <Card>
        <CardHeader>
          <CardTitle>Inventory Items</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Stock Status</TableHead>
                  <TableHead>Batch/Expiry</TableHead>
                  <TableHead>Pricing</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Supplier</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredInventory.map((item) => {
                  const stockStatus = getStockStatus(item.currentStock, item.minStock, item.reorderPoint);
                  const isExpiringSoon = new Date(item.expiryDate) <= new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000);
                  
                  return (
                    <TableRow key={item.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{item.name}</p>
                          <div className="flex items-center gap-2 text-sm text-gray-500">
                            <span>{item.sku}</span>
                            <span>•</span>
                            <span className="flex items-center gap-1">
                              <Barcode className="h-3 w-3" />
                              {item.barcode}
                            </span>
                          </div>
                          <p className="text-xs text-gray-400">{item.category}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Badge variant={stockStatus.status === "critical" ? "destructive" : "default"}>
                            {item.currentStock}
                          </Badge>
                          <span className="text-sm text-gray-500">/ {item.maxStock}</span>
                          {stockStatus.status === "critical" && (
                            <AlertTriangle className="h-4 w-4 text-red-500" />
                          )}
                        </div>
                        <div className="text-xs text-gray-400 mt-1">
                          <div>Min: {item.minStock} | Reorder: {item.reorderPoint}</div>
                          <Badge variant="outline" className={`text-xs ${stockStatus.status === 'critical' ? 'border-red-500 text-red-600' : stockStatus.status === 'low' ? 'border-orange-500 text-orange-600' : 'border-green-500 text-green-600'}`}>
                            {stockStatus.text}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <p className="font-medium">{item.batchNumber}</p>
                          <p className={`text-xs ${isExpiringSoon ? 'text-orange-600 font-medium' : 'text-gray-500'}`}>
                            Expires: {item.expiryDate}
                          </p>
                          <p className="text-xs text-gray-400">{item.warrantyMonths}mo warranty</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <p className="font-medium">Sell: ${item.unitPrice}</p>
                          <p className="text-xs text-gray-500">Cost: ${item.unitCost}</p>
                          <p className="text-xs text-gray-400">
                            Total: ${(item.currentStock * item.unitCost).toFixed(2)}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <p className="text-sm font-medium">{item.location}</p>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <p className="font-medium">{item.supplier}</p>
                          <p className="text-xs text-gray-500">Last: {item.lastRestocked}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col gap-1">
                          <div className="flex gap-1">
                            <Button variant="outline" size="sm">
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => openTransferModal(item)}
                            >
                              <ArrowRightLeft className="h-3 w-3" />
                            </Button>
                          </div>
                          <Button variant="outline" size="sm" className="text-xs">
                            Restock
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Modals */}
      <StockReceivingModal
        isOpen={showReceivingModal}
        onClose={() => setShowReceivingModal(false)}
        onReceive={handleReceiveStock}
      />

      <StockTransferModal
        isOpen={showTransferModal}
        onClose={() => setShowTransferModal(false)}
        onTransfer={handleTransferStock}
        selectedItem={selectedItem}
      />

      <PurchaseOrderModal
        isOpen={showPOModal}
        onClose={() => setShowPOModal(false)}
        onCreatePO={handleCreatePO}
      />
    </div>
  );
};

export default Inventory;
