import { useState, useEffect } from "react";
import { BarChart, Bar, LineChart, Line, PieChart, Pie, Cell, AreaChart, Area, ResponsiveContainer, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from "recharts";
import { TrendingUp, TrendingDown, Target, AlertTriangle, Users, Package, DollarSign, Calendar, Filter, Download, Zap, ShoppingCart, Gauge, Brain } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/hooks/use-toast";

// AI Components
import { AIForecastingPanel } from "@/components/ai/AIForecastingPanel";
import { AIChatbot } from "@/components/ai/AIChatbot";
import { AIInsightsPanel } from "@/components/ai/AIInsightsPanel";

const BusinessIntelligence = () => {
  const [timeframe, setTimeframe] = useState("30d");
  const [selectedKPI, setSelectedKPI] = useState("revenue");
  const { toast } = useToast();

  const forecastData = [
    { month: 'Jan', actual: 45000, forecast: 42000, confidence: 85 },
    { month: 'Feb', actual: 52000, forecast: 49000, confidence: 88 },
    { month: 'Mar', actual: 48000, forecast: 51000, confidence: 82 },
    { month: 'Apr', actual: null, forecast: 55000, confidence: 79 },
    { month: 'May', actual: null, forecast: 58000, confidence: 76 },
    { month: 'Jun', actual: null, forecast: 61000, confidence: 73 },
  ];

  const customerSegmentData = [
    { segment: 'High Value', customers: 45, revenue: 125000, avgOrder: 2778, color: '#22c55e' },
    { segment: 'Regular', customers: 128, revenue: 89000, avgOrder: 695, color: '#3b82f6' },
    { segment: 'Occasional', customers: 89, revenue: 34000, avgOrder: 382, color: '#f59e0b' },
    { segment: 'New', customers: 67, revenue: 18000, avgOrder: 269, color: '#ef4444' },
  ];

  const inventoryOptimizationData = [
    { category: 'Deep Cycle', currentStock: 45, optimalStock: 52, stockoutRisk: 'Medium', reorderPoint: 35 },
    { category: 'Automotive', currentStock: 78, optimalStock: 65, stockoutRisk: 'Low', reorderPoint: 42 },
    { category: 'Marine', currentStock: 23, optimalStock: 38, stockoutRisk: 'High', reorderPoint: 28 },
    { category: 'Motorcycle', currentStock: 34, optimalStock: 31, stockoutRisk: 'Low', reorderPoint: 18 },
  ];

  const performanceKPIs = [
    { name: 'Revenue Growth', value: 23.5, target: 20, trend: 'up', unit: '%' },
    { name: 'Customer Retention', value: 87.2, target: 85, trend: 'up', unit: '%' },
    { name: 'Inventory Turnover', value: 6.8, target: 8, trend: 'down', unit: 'x' },
    { name: 'Gross Margin', value: 34.7, target: 35, trend: 'down', unit: '%' },
    { name: 'Order Fulfillment', value: 96.3, target: 95, trend: 'up', unit: '%' },
    { name: 'Customer Acquisition Cost', value: 45.20, target: 50, trend: 'up', unit: '$' },
  ];

  const seasonalTrends = [
    { month: 'Jan', sales: 38, temperature: 32 },
    { month: 'Feb', sales: 42, temperature: 35 },
    { month: 'Mar', sales: 48, temperature: 45 },
    { month: 'Apr', sales: 52, temperature: 58 },
    { month: 'May', sales: 61, temperature: 68 },
    { month: 'Jun', sales: 58, temperature: 78 },
    { month: 'Jul', sales: 54, temperature: 82 },
    { month: 'Aug', sales: 56, temperature: 80 },
    { month: 'Sep', sales: 62, temperature: 72 },
    { month: 'Oct', sales: 68, temperature: 62 },
    { month: 'Nov', sales: 72, temperature: 48 },
    { month: 'Dec', sales: 75, temperature: 38 },
  ];

  const handleExportDashboard = () => {
    toast({
      title: "Dashboard Export Started",
      description: "Your BI dashboard report is being generated and will be downloaded shortly.",
    });
  };

  // Prepare dashboard data for AI chatbot
  const dashboardData = {
    kpis: performanceKPIs,
    forecast: forecastData,
    customerSegments: customerSegmentData,
    inventory: inventoryOptimizationData,
    seasonalTrends: seasonalTrends
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'High': return 'text-red-600 bg-red-100';
      case 'Medium': return 'text-yellow-600 bg-yellow-100';
      case 'Low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getKPIColor = (kpi: any) => {
    if (kpi.name === 'Customer Acquisition Cost') {
      return kpi.value < kpi.target ? 'text-green-600' : 'text-red-600';
    }
    return kpi.value >= kpi.target ? 'text-green-600' : 'text-red-600';
  };

  return (
    <div className="p-3 space-y-3 bg-gray-50 min-h-screen">
      {/* Compact Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl p-4 text-white shadow-lg">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-bold flex items-center">
              <Brain className="h-5 w-5 mr-2" />
              AI Business Intelligence
            </h1>
            <p className="text-purple-100 text-sm mt-1">Real-time analytics powered by artificial intelligence</p>
          </div>
          <div className="flex gap-2">
            <Select value={timeframe} onValueChange={setTimeframe}>
              <SelectTrigger className="w-28 bg-white/10 border-white/20 text-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">7 days</SelectItem>
                <SelectItem value="30d">30 days</SelectItem>
                <SelectItem value="90d">90 days</SelectItem>
                <SelectItem value="1y">1 year</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={handleExportDashboard} className="bg-white/10 hover:bg-white/20 border-white/20 text-white">
              <Download className="h-4 w-4 mr-1" />
              Export
            </Button>
          </div>
        </div>
      </div>

      {/* KPI Overview - Improved Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
        {performanceKPIs.map((kpi, index) => (
          <Card key={index} className="bg-white shadow-sm border hover:shadow-md transition-all duration-200 hover:scale-105">
            <CardContent className="p-3">
              <div className="flex items-center justify-between mb-2">
                <div className="text-xs font-medium text-gray-600 leading-tight">{kpi.name}</div>
                {kpi.trend === 'up' ? 
                  <TrendingUp className={`h-3 w-3 ${getKPIColor(kpi)}`} /> : 
                  <TrendingDown className={`h-3 w-3 ${getKPIColor(kpi)}`} />
                }
              </div>
              <div className="text-lg font-bold mb-1">
                {kpi.unit === '$' ? '$' : ''}{kpi.value}{kpi.unit !== '$' ? kpi.unit : ''}
              </div>
              <div className="text-xs text-gray-500 mb-2">
                Target: {kpi.unit === '$' ? '$' : ''}{kpi.target}{kpi.unit !== '$' ? kpi.unit : ''}
              </div>
              <Progress 
                value={(kpi.value / kpi.target) * 100} 
                className="h-1"
              />
            </CardContent>
          </Card>
        ))}
      </div>

      {/* AI Features Row - Side by Side */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <AIInsightsPanel kpiData={performanceKPIs} />
        <AIChatbot dashboardData={dashboardData} />
      </div>

      {/* Main Dashboard Content */}
      <Tabs defaultValue="ai-forecasting" className="space-y-3">
        <div className="bg-white rounded-lg p-1 shadow-sm border">
          <TabsList className="grid w-full grid-cols-5 bg-gray-50">
            <TabsTrigger value="ai-forecasting" className="text-xs">AI Forecasting</TabsTrigger>
            <TabsTrigger value="customer-insights" className="text-xs">Customers</TabsTrigger>
            <TabsTrigger value="inventory-optimization" className="text-xs">Inventory</TabsTrigger>
            <TabsTrigger value="seasonal-analysis" className="text-xs">Seasonal</TabsTrigger>
            <TabsTrigger value="traditional-forecasting" className="text-xs">Traditional</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="ai-forecasting" className="space-y-3">
          <AIForecastingPanel historicalData={forecastData} />
        </TabsContent>

        <TabsContent value="traditional-forecasting" className="space-y-3">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <Card className="lg:col-span-2 bg-white shadow-sm">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center text-lg">
                  <Target className="h-4 w-4 mr-2" />
                  Revenue Forecast vs Actual
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={280}>
                  <LineChart data={forecastData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value, name) => [`$${value?.toLocaleString()}`, name]} />
                    <Legend />
                    <Line type="monotone" dataKey="actual" stroke="#22c55e" strokeWidth={3} name="Actual Revenue" />
                    <Line type="monotone" dataKey="forecast" stroke="#3b82f6" strokeWidth={3} strokeDasharray="5 5" name="Forecasted Revenue" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card className="bg-white shadow-sm">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Forecast Confidence</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {forecastData.slice(3).map((item, index) => (
                    <div key={index} className="p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <p className="font-semibold text-sm">{item.month}</p>
                          <p className="text-xs text-gray-600">${item.forecast?.toLocaleString()}</p>
                        </div>
                        <Badge className={`text-xs px-2 py-1 ${item.confidence >= 80 ? 'bg-green-100 text-green-700' : item.confidence >= 70 ? 'bg-yellow-100 text-yellow-700' : 'bg-red-100 text-red-700'}`}>
                          {item.confidence}%
                        </Badge>
                      </div>
                      <Progress value={item.confidence} className="h-2" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="customer-insights" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            <Card className="bg-white shadow-sm">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center text-lg">
                  <Users className="h-4 w-4 mr-2" />
                  Customer Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={220}>
                  <PieChart>
                    <Pie
                      data={customerSegmentData}
                      cx="50%"
                      cy="50%"
                      outerRadius={90}
                      fill="#8884d8"
                      dataKey="customers"
                      label={({ segment, percent }) => `${segment} ${(percent * 100).toFixed(0)}%`}
                    >
                      {customerSegmentData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card className="lg:col-span-2 bg-white shadow-sm">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Segment Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {customerSegmentData.map((segment, index) => (
                    <div key={index} className="border rounded-lg p-4 hover:shadow-md transition-shadow bg-gradient-to-br from-white to-gray-50">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="font-bold text-sm" style={{ color: segment.color }}>
                          {segment.segment}
                        </h3>
                        <Badge variant="outline" className="text-xs">{segment.customers} customers</Badge>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-xs text-gray-600">Revenue</span>
                          <span className="text-sm font-semibold">${segment.revenue.toLocaleString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-xs text-gray-600">Avg Order</span>
                          <span className="text-sm font-semibold">${segment.avgOrder}</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="h-2 rounded-full" 
                            style={{ backgroundColor: segment.color, width: `${(segment.revenue / 125000) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="inventory-optimization" className="space-y-4">
          <Card className="bg-white shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-lg">
                <Package className="h-4 w-4 mr-2" />
                Smart Inventory Management
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {inventoryOptimizationData.map((item, index) => (
                  <div key={index} className="border rounded-xl p-4 bg-gradient-to-br from-white to-gray-50 hover:shadow-lg transition-all duration-200">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-bold text-sm">{item.category}</h3>
                      <Badge className={`${getRiskColor(item.stockoutRisk)} text-xs px-2 py-1`}>
                        {item.stockoutRisk}
                      </Badge>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-gray-600">Current Stock</span>
                        <span className="font-semibold text-lg">{item.currentStock}</span>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-gray-600">Optimal Stock</span>
                        <span className="font-medium text-sm text-blue-600">{item.optimalStock}</span>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-xs text-gray-600">Reorder Point</span>
                        <span className="font-medium text-sm text-orange-600">{item.reorderPoint}</span>
                      </div>
                      
                      <div className="pt-2">
                        {item.currentStock < item.reorderPoint ? (
                          <Button size="sm" variant="outline" className="w-full text-orange-600 border-orange-600 text-xs hover:bg-orange-50">
                            <AlertTriangle className="h-3 w-3 mr-1" />
                            Reorder Now
                          </Button>
                        ) : item.currentStock > item.optimalStock ? (
                          <Badge variant="secondary" className="w-full justify-center text-xs py-1">Overstocked</Badge>
                        ) : (
                          <Badge variant="outline" className="w-full justify-center text-green-600 text-xs py-1">Optimal Level</Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="seasonal-analysis" className="space-y-4">
          <Card className="bg-white shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center text-lg">
                <Calendar className="h-4 w-4 mr-2" />
                Seasonal Sales Intelligence
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={320}>
                <AreaChart data={seasonalTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis yAxisId="left" />
                  <YAxis yAxisId="right" orientation="right" />
                  <Tooltip />
                  <Legend />
                  <Area 
                    yAxisId="left"
                    type="monotone" 
                    dataKey="sales" 
                    stroke="#3b82f6" 
                    fill="#3b82f6" 
                    fillOpacity={0.3}
                    name="Sales Volume"
                    strokeWidth={3}
                  />
                  <Line 
                    yAxisId="right"
                    type="monotone" 
                    dataKey="temperature" 
                    stroke="#ef4444" 
                    strokeWidth={3}
                    name="Avg Temperature (°F)"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card className="bg-white shadow-sm">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Seasonal Peaks</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg border-l-4 border-blue-500">
                    <div>
                      <p className="font-semibold text-sm">Winter Peak (Nov-Dec)</p>
                      <p className="text-xs text-gray-600">Cold weather battery replacements</p>
                    </div>
                    <Badge className="bg-blue-600 text-white text-xs px-3 py-1">+35%</Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border-l-4 border-green-500">
                    <div>
                      <p className="font-semibold text-sm">Spring Surge (Mar-May)</p>
                      <p className="text-xs text-gray-600">Marine & outdoor equipment prep</p>
                    </div>
                    <Badge className="bg-green-600 text-white text-xs px-3 py-1">+28%</Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-gradient-to-r from-orange-50 to-orange-100 rounded-lg border-l-4 border-orange-500">
                    <div>
                      <p className="font-semibold text-sm">Summer Maintenance (Jun-Aug)</p>
                      <p className="text-xs text-gray-600">Heat-related battery stress</p>
                    </div>
                    <Badge className="bg-orange-600 text-white text-xs px-3 py-1">+15%</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white shadow-sm">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">AI Recommendations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-start space-x-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="p-2 bg-yellow-100 rounded-full">
                      <Zap className="h-4 w-4 text-yellow-600" />
                    </div>
                    <div>
                      <p className="font-semibold text-sm">Stock Up for Winter</p>
                      <p className="text-xs text-gray-600 mt-1">Increase automotive battery inventory by 40% before November</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="p-2 bg-blue-100 rounded-full">
                      <ShoppingCart className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-semibold text-sm">Marine Season Prep</p>
                      <p className="text-xs text-gray-600 mt-1">Boost marine battery stock 3 weeks before spring</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="p-2 bg-green-100 rounded-full">
                      <Gauge className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <p className="font-semibold text-sm">Dynamic Pricing</p>
                      <p className="text-xs text-gray-600 mt-1">Implement smart pricing adjustments during peak seasons</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default BusinessIntelligence;
