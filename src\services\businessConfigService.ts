import { supabase } from '@/integrations/supabase/client';
import { 
  BusinessConfiguration, 
  ProductCategory, 
  ProductAttribute, 
  IndustryTemplate,
  BusinessTerminology 
} from '@/types/business';
import { INDUSTRY_TEMPLATES } from '@/data/industryTemplates';

export class BusinessConfigService {
  private static instance: BusinessConfigService;
  private currentConfig: BusinessConfiguration | null = null;
  private categories: ProductCategory[] = [];
  private attributes: ProductAttribute[] = [];

  private constructor() {}

  static getInstance(): BusinessConfigService {
    if (!BusinessConfigService.instance) {
      BusinessConfigService.instance = new BusinessConfigService();
    }
    return BusinessConfigService.instance;
  }

  // Get current business configuration
  async getCurrentConfig(): Promise<BusinessConfiguration | null> {
    if (this.currentConfig) {
      return this.currentConfig;
    }

    try {
      const { data, error } = await supabase
        .from('business_configurations')
        .select('*')
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }

      this.currentConfig = data;
      return data;
    } catch (error) {
      console.error('Error fetching business configuration:', error);
      return null;
    }
  }

  // Create or update business configuration
  async saveConfig(config: Partial<BusinessConfiguration>): Promise<BusinessConfiguration> {
    try {
      const { data, error } = await supabase
        .from('business_configurations')
        .upsert({
          ...config,
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;

      this.currentConfig = data;
      return data;
    } catch (error) {
      console.error('Error saving business configuration:', error);
      throw error;
    }
  }

  // Initialize business from template
  async initializeFromTemplate(
    businessName: string, 
    templateId: string
  ): Promise<BusinessConfiguration> {
    const template = INDUSTRY_TEMPLATES.find(t => t.id === templateId);
    if (!template) {
      throw new Error(`Template ${templateId} not found`);
    }

    try {
      // Create business configuration
      const config = await this.saveConfig({
        business_name: businessName,
        business_type: template.businessType,
        industry_template: templateId,
        terminology: template.terminology
      });

      // Create default categories
      await this.createDefaultCategories(config.id, template);

      // Create default attributes
      await this.createDefaultAttributes(config.id, template);

      return config;
    } catch (error) {
      console.error('Error initializing from template:', error);
      throw error;
    }
  }

  // Get categories for current business
  async getCategories(): Promise<ProductCategory[]> {
    const config = await this.getCurrentConfig();
    if (!config) {
      // Return default categories when no config is available
      return [
        { id: '1', name: 'General', description: 'General products', color: 'bg-blue-500', sortOrder: 1, isActive: true, businessId: '', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
        { id: '2', name: 'Electronics', description: 'Electronic items', color: 'bg-green-500', sortOrder: 2, isActive: true, businessId: '', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
        { id: '3', name: 'Accessories', description: 'Product accessories', color: 'bg-purple-500', sortOrder: 3, isActive: true, businessId: '', createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() }
      ];
    }

    try {
      const { data, error } = await supabase
        .from('product_categories')
        .select('*')
        .eq('business_id', config.id)
        .eq('is_active', true)
        .order('sort_order');

      if (error) throw error;

      this.categories = data || [];
      return this.categories;
    } catch (error) {
      console.error('Error fetching categories:', error);
      return [];
    }
  }

  // Get attributes for current business
  async getAttributes(): Promise<ProductAttribute[]> {
    const config = await this.getCurrentConfig();
    if (!config) {
      // Return default attributes when no config is available
      return [
        { id: '1', name: 'Brand', type: 'text', description: 'Product brand', isRequired: false, sortOrder: 1, isActive: true, businessId: '', options: null, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
        { id: '2', name: 'Model', type: 'text', description: 'Product model', isRequired: false, sortOrder: 2, isActive: true, businessId: '', options: null, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() },
        { id: '3', name: 'Color', type: 'select', description: 'Product color', isRequired: false, sortOrder: 3, isActive: true, businessId: '', options: ['Red', 'Blue', 'Green', 'Black', 'White'], createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() }
      ];
    }

    try {
      const { data, error } = await supabase
        .from('product_attributes')
        .select('*')
        .eq('business_id', config.id)
        .eq('is_active', true)
        .order('sort_order');

      if (error) throw error;

      this.attributes = data || [];
      return this.attributes;
    } catch (error) {
      console.error('Error fetching attributes:', error);
      return [];
    }
  }

  // Get terminology for current business
  getTerminology(): BusinessTerminology {
    if (this.currentConfig?.terminology) {
      return this.currentConfig.terminology as BusinessTerminology;
    }

    // Default terminology
    return {
      product: {
        singular: 'Product',
        plural: 'Products',
        singularLower: 'product',
        pluralLower: 'products'
      },
      inventory: {
        stock: 'Stock',
        warehouse: 'Warehouse',
        category: 'Category',
        stockLower: 'stock',
        warehouseLower: 'warehouse',
        categoryLower: 'category'
      },
      sales: {
        customer: 'Customer',
        order: 'Order',
        orderPlural: 'Orders',
        receipt: 'Receipt',
        transaction: 'Sale',
        transactionPlural: 'Sales',
        customerLower: 'customer',
        orderLower: 'order',
        receiptLower: 'receipt'
      },
      custom: {}
    };
  }

  // Create category
  async createCategory(category: Omit<ProductCategory, 'id' | 'createdAt' | 'updatedAt'>): Promise<ProductCategory> {
    try {
      const { data, error } = await supabase
        .from('product_categories')
        .insert({
          ...category,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating category:', error);
      throw error;
    }
  }

  // Create attribute
  async createAttribute(attribute: Omit<ProductAttribute, 'id' | 'createdAt' | 'updatedAt'>): Promise<ProductAttribute> {
    try {
      const { data, error } = await supabase
        .from('product_attributes')
        .insert({
          ...attribute,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating attribute:', error);
      throw error;
    }
  }

  // Private helper methods
  private async createDefaultCategories(businessId: string, template: IndustryTemplate): Promise<void> {
    const categories = template.defaultCategories.map(cat => ({
      business_id: businessId,
      name: cat.name,
      description: cat.description || null,
      color: cat.color,
      icon: cat.icon || null,
      parent_id: null, // TODO: Handle parent categories
      sort_order: cat.sortOrder,
      is_active: true
    }));

    const { error } = await supabase
      .from('product_categories')
      .insert(categories);

    if (error) throw error;
  }

  private async createDefaultAttributes(businessId: string, template: IndustryTemplate): Promise<void> {
    const attributes = template.defaultAttributes.map(attr => ({
      business_id: businessId,
      name: attr.name,
      label: attr.label,
      type: attr.type,
      options: attr.options || null,
      is_required: attr.isRequired,
      sort_order: attr.sortOrder,
      is_active: true
    }));

    const { error } = await supabase
      .from('product_attributes')
      .insert(attributes);

    if (error) throw error;
  }

  // Clear cached data
  clearCache(): void {
    this.currentConfig = null;
    this.categories = [];
    this.attributes = [];
  }
}

export const businessConfigService = BusinessConfigService.getInstance();
