
import { supabase } from "@/integrations/supabase/client";

export interface InventoryAnomaly {
  itemId: number;
  severity: 'low' | 'medium' | 'high';
  deviation: number;
  reason: string;
  recommendedAction: string;
}

export interface StockPrediction {
  itemId: number;
  itemName: string;
  predictedLevel: number;
  trend: 'declining' | 'stable' | 'increasing';
  riskLevel: 'low' | 'medium' | 'high';
  recommendations: string;
}

export interface TheftAlert {
  type: string;
  severity: 'low' | 'medium' | 'high';
  description: string;
  employee?: string;
  location?: string;
  actionRequired: string;
}

export interface ComprehensiveAnalysis {
  overallHealth: string;
  keyInsights: string[];
  recommendations: string[];
  riskFactors: string[];
  analysisDate: string;
}

export class AIStockAnalysisService {
  constructor() {
    // No API key needed - handled securely by Edge Function
  }

  async detectAnomalies(stockTakeData: any[], historicalData: any[]): Promise<{
    anomalies: InventoryAnomaly[];
    overallRisk: string;
    confidence: number;
  }> {
    try {
      console.log('Calling AI edge function for anomaly detection');
      
      const { data, error } = await supabase.functions.invoke('grok-ai-stock-analysis', {
        body: {
          action: 'anomaly-detection',
          payload: { stockTakeData, historicalData }
        }
      });

      if (error) {
        console.error('Edge function error:', error);
        throw new Error('Failed to detect anomalies');
      }

      return data;
    } catch (error) {
      console.error('Anomaly detection error:', error);
      return this.getMockAnomalies();
    }
  }

  async generatePredictions(historicalData: any[], currentData: any[]): Promise<{
    predictions: StockPrediction[];
    accuracy: number;
    analysisDate: string;
  }> {
    try {
      console.log('Calling AI edge function for predictive analytics');
      
      const { data, error } = await supabase.functions.invoke('grok-ai-stock-analysis', {
        body: {
          action: 'predictive-analytics',
          payload: { historicalData, currentData }
        }
      });

      if (error) {
        console.error('Edge function error:', error);
        throw new Error('Failed to generate predictions');
      }

      return data;
    } catch (error) {
      console.error('Prediction generation error:', error);
      return this.getMockPredictions();
    }
  }

  async analyzeTheftPatterns(stockTakeData: any[], employeeData: any[], locationData: any[]): Promise<{
    alerts: TheftAlert[];
    riskScore: number;
    confidenceLevel: number;
  }> {
    try {
      console.log('Calling AI edge function for theft pattern analysis');
      
      const { data, error } = await supabase.functions.invoke('grok-ai-stock-analysis', {
        body: {
          action: 'theft-detection',
          payload: { stockTakeData, employeeData, locationData }
        }
      });

      if (error) {
        console.error('Edge function error:', error);
        throw new Error('Failed to analyze theft patterns');
      }

      return data;
    } catch (error) {
      console.error('Theft analysis error:', error);
      return this.getMockTheftAnalysis();
    }
  }

  async performComprehensiveAnalysis(stockTakeData: any[], historicalData: any[]): Promise<ComprehensiveAnalysis> {
    try {
      console.log('Calling AI edge function for comprehensive analysis');
      
      const { data, error } = await supabase.functions.invoke('grok-ai-stock-analysis', {
        body: {
          action: 'comprehensive-analysis',
          payload: { stockTakeData, historicalData }
        }
      });

      if (error) {
        console.error('Edge function error:', error);
        throw new Error('Failed to perform comprehensive analysis');
      }

      return data;
    } catch (error) {
      console.error('Comprehensive analysis error:', error);
      return this.getMockComprehensiveAnalysis();
    }
  }

  private getMockAnomalies() {
    return {
      anomalies: [
        { itemId: 1, severity: 'high' as const, deviation: -13.3, reason: 'Significant stock variance detected in AGM batteries', recommendedAction: 'Immediate recount and investigation' },
        { itemId: 3, severity: 'medium' as const, deviation: 8.2, reason: 'Unusual increase in marine battery count', recommendedAction: 'Verify receiving records' }
      ],
      overallRisk: 'medium',
      confidence: 87.5
    };
  }

  private getMockPredictions() {
    return {
      predictions: [
        { itemId: 1, itemName: 'AGM-12V-100', predictedLevel: 8, trend: 'declining' as const, riskLevel: 'high' as const, recommendations: 'Reorder immediately - projected stockout in 5 days' },
        { itemId: 2, itemName: 'CAR-550', predictedLevel: 15, trend: 'stable' as const, riskLevel: 'low' as const, recommendations: 'Normal inventory levels maintained' },
        { itemId: 3, itemName: 'MAR-105', predictedLevel: 20, trend: 'increasing' as const, riskLevel: 'medium' as const, recommendations: 'Monitor for overstock situation' }
      ],
      accuracy: 92.3,
      analysisDate: new Date().toISOString()
    };
  }

  private getMockTheftAnalysis() {
    return {
      alerts: [
        { type: 'employee_pattern', severity: 'high' as const, description: 'Repeated variances detected with specific employee', employee: 'Employee #3', location: 'Warehouse A', actionRequired: 'Investigation recommended' },
        { type: 'location_anomaly', severity: 'medium' as const, description: 'Unusual variance pattern in specific location', location: 'Warehouse B - Section C2', actionRequired: 'Enhanced monitoring' }
      ],
      riskScore: 6.8,
      confidenceLevel: 81.2
    };
  }

  private getMockComprehensiveAnalysis(): ComprehensiveAnalysis {
    return {
      overallHealth: 'good',
      keyInsights: [
        'Inventory accuracy has improved by 15% over last quarter',
        'Deep cycle batteries show highest variance rates',
        'Warehouse A consistently outperforms other locations'
      ],
      recommendations: [
        'Implement enhanced monitoring for high-value items',
        'Provide additional training for counting procedures',
        'Consider automated counting systems for critical inventory'
      ],
      riskFactors: ['Seasonal demand fluctuations', 'New employee training gaps'],
      analysisDate: new Date().toISOString()
    };
  }
}
