
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Barcode, Plus, Trash2, Search } from "lucide-react";

interface SerialNumberModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedItem: any;
}

const SerialNumberModal = ({ isOpen, onClose, selectedItem }: SerialNumberModalProps) => {
  const [newSerial, setNewSerial] = useState("");
  const [searchSerial, setSearchSerial] = useState("");
  const [serialNumbers, setSerialNumbers] = useState([
    { id: 1, serial: "AGM001", status: "In Stock", purchaseDate: "2024-01-15", warrantyExpiry: "2026-01-15", location: "A1-B2" },
    { id: 2, serial: "AGM002", status: "Sold", purchaseDate: "2024-01-10", warrantyExpiry: "2026-01-10", soldDate: "2024-01-20", customer: "John Doe" },
    { id: 3, serial: "AGM003", status: "In Stock", purchaseDate: "2024-01-12", warrantyExpiry: "2026-01-12", location: "A1-B2" }
  ]);

  const addSerialNumber = () => {
    if (newSerial.trim()) {
      const newEntry = {
        id: Date.now(),
        serial: newSerial.trim(),
        status: "In Stock",
        purchaseDate: new Date().toISOString().split('T')[0],
        warrantyExpiry: new Date(Date.now() + 24 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 24 months
        location: "Pending"
      };
      setSerialNumbers([...serialNumbers, newEntry]);
      setNewSerial("");
    }
  };

  const removeSerialNumber = (id: number) => {
    setSerialNumbers(serialNumbers.filter(item => item.id !== id));
  };

  const filteredSerials = serialNumbers.filter(item =>
    item.serial.toLowerCase().includes(searchSerial.toLowerCase())
  );

  const getStatusBadge = (status: string) => {
    const colors = {
      "In Stock": "default",
      "Sold": "secondary",
      "Returned": "destructive",
      "Under Warranty": "default"
    };
    return <Badge variant={colors[status] || "default"}>{status}</Badge>;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Barcode className="h-5 w-5" />
            Serial Number Management
            {selectedItem && <span className="text-sm font-normal text-gray-500">- {selectedItem.name}</span>}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Add New Serial */}
          <div className="flex gap-2">
            <div className="flex-1">
              <Label htmlFor="newSerial">Add New Serial Number</Label>
              <Input
                id="newSerial"
                value={newSerial}
                onChange={(e) => setNewSerial(e.target.value)}
                placeholder="Enter serial number"
                onKeyPress={(e) => e.key === 'Enter' && addSerialNumber()}
              />
            </div>
            <div className="flex items-end">
              <Button onClick={addSerialNumber}>
                <Plus className="h-4 w-4 mr-2" />
                Add Serial
              </Button>
            </div>
          </div>

          {/* Search */}
          <div className="relative">
            <Search className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
            <Input
              placeholder="Search serial numbers..."
              value={searchSerial}
              onChange={(e) => setSearchSerial(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Serial Numbers Table */}
          <div className="border rounded-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Serial Number</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Purchase Date</TableHead>
                  <TableHead>Warranty Expiry</TableHead>
                  <TableHead>Location/Customer</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredSerials.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <div className="font-mono font-medium">{item.serial}</div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(item.status)}
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">{item.purchaseDate}</div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">{item.warrantyExpiry}</div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {item.status === 'Sold' ? item.customer : item.location}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeSerialNumber(item.id)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Summary */}
          <div className="grid grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {serialNumbers.filter(s => s.status === 'In Stock').length}
              </div>
              <div className="text-sm text-gray-600">In Stock</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {serialNumbers.filter(s => s.status === 'Sold').length}
              </div>
              <div className="text-sm text-gray-600">Sold</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">
                {serialNumbers.length}
              </div>
              <div className="text-sm text-gray-600">Total Tracked</div>
            </div>
          </div>

          <div className="flex justify-end">
            <Button variant="outline" onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SerialNumberModal;
