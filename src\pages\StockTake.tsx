import { useState, useEffect } from "react";
import { ClipboardList, Camera, MessageSquare, CheckCircle, AlertTriangle, Search, Filter, Download, Plus, Edit, Trash2, Eye, Brain, Shield, TrendingUp, Zap } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Progress } from "@/components/ui/progress";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { AIStockAnalysisService } from "@/components/ai/AIStockAnalysisService";

const StockTake = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedItems, setSelectedItems] = useState<number[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showCountModal, setShowCountModal] = useState(false);
  const [selectedStockTake, setSelectedStockTake] = useState<any>(null);
  const [selectedCountItem, setSelectedCountItem] = useState<any>(null);
  const [aiAnalysisLoading, setAiAnalysisLoading] = useState(false);
  const [aiInsights, setAiInsights] = useState<any>(null);
  const [theftAlerts, setTheftAlerts] = useState<any[]>([]);
  const [predictions, setPredictions] = useState<any[]>([]);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showTheftModal, setShowTheftModal] = useState(false);
  const [showNotesModal, setShowNotesModal] = useState(false);
  const [showPhotosModal, setShowPhotosModal] = useState(false);
  const [showAIAnalysisModal, setShowAIAnalysisModal] = useState(false);
  const [selectedItemForAction, setSelectedItemForAction] = useState<any>(null);
  const { toast } = useToast();

  const aiService = new AIStockAnalysisService();

  // New stock take form state
  const [newStockTake, setNewStockTake] = useState({
    name: "",
    description: "",
    startDate: "",
    endDate: "",
    employeeName: "",
    locations: [] as string[],
    includeCategories: [] as string[]
  });

  // Sample stock take data
  const [stockTakes, setStockTakes] = useState([
    {
      id: 1,
      name: "Monthly Stock Count - January 2024",
      status: "active",
      startDate: "2024-01-20",
      endDate: "2024-01-25",
      totalItems: 150,
      countedItems: 89,
      progress: 59,
      variances: 5,
      createdBy: "John Manager",
      locations: ["Warehouse A", "Warehouse B"],
      description: "Regular monthly stock verification across all locations"
    },
    {
      id: 2,
      name: "Year-End Audit Count",
      status: "completed",
      startDate: "2023-12-28",
      endDate: "2024-01-05",
      totalItems: 300,
      countedItems: 300,
      progress: 100,
      variances: 12,
      createdBy: "Sarah Auditor",
      locations: ["All Locations"],
      description: "Complete inventory audit for year-end financial reporting"
    },
    {
      id: 3,
      name: "Cycle Count - Deep Cycle Batteries",
      status: "draft",
      startDate: "2024-01-25",
      endDate: "2024-01-27",
      totalItems: 45,
      countedItems: 0,
      progress: 0,
      variances: 0,
      createdBy: "Mike Counter",
      locations: ["Warehouse A"],
      description: "Focused count on deep cycle battery category"
    }
  ]);

  // Sample count sheet data
  const [countSheetItems, setCountSheetItems] = useState([
    {
      id: 1,
      sku: "AGM-12V-100",
      name: "12V Deep Cycle AGM Battery",
      location: "A1-B2",
      systemCount: 15,
      physicalCount: 13,
      variance: -2,
      status: "counted",
      countedBy: "John Counter",
      countDate: "2024-01-21",
      notes: "2 units found damaged, moved to RMA area",
      photos: ["photo1.jpg"],
      batchNumber: "BCH-2024-001"
    },
    {
      id: 2,
      sku: "CAR-550",
      name: "Car Battery 550CCA",
      location: "B1-A3",
      systemCount: 2,
      physicalCount: 2,
      variance: 0,
      status: "counted",
      countedBy: "Sarah Counter",
      countDate: "2024-01-21",
      notes: "",
      photos: [],
      batchNumber: "BCH-2024-002"
    },
    {
      id: 3,
      sku: "MAR-105",
      name: "Marine Battery 105Ah",
      location: "C2-B1",
      systemCount: 12,
      physicalCount: null,
      variance: null,
      status: "pending",
      countedBy: null,
      countDate: null,
      notes: "",
      photos: [],
      batchNumber: "BCH-2024-003"
    }
  ]);

  const availableLocations = ["Warehouse A", "Warehouse B", "Warehouse C", "Store Front", "All Locations"];
  const availableCategories = ["Deep Cycle Batteries", "Car Batteries", "Marine Batteries", "Motorcycle Batteries", "All Categories"];

  // AI Analysis Functions
  const runAIAnalysis = async () => {
    setAiAnalysisLoading(true);
    try {
      const [anomalies, theftAnalysis, stockPredictions, comprehensiveAnalysis] = await Promise.all([
        aiService.detectAnomalies(countSheetItems, stockTakes),
        aiService.analyzeTheftPatterns(countSheetItems, stockTakes.map(st => ({ name: st.createdBy })), availableLocations.map(loc => ({ name: loc }))),
        aiService.generatePredictions(stockTakes, countSheetItems),
        aiService.performComprehensiveAnalysis(countSheetItems, stockTakes)
      ]);

      setAiInsights(comprehensiveAnalysis);
      setTheftAlerts(theftAnalysis.alerts);
      setPredictions(stockPredictions.predictions);

      toast({
        title: "AI Analysis Complete",
        description: `Analysis completed with ${comprehensiveAnalysis.overallHealth} inventory health status.`
      });
    } catch (error) {
      toast({
        title: "AI Analysis Error",
        description: "Failed to complete AI analysis. Using fallback data.",
        variant: "destructive"
      });
    } finally {
      setAiAnalysisLoading(false);
    }
  };

  useEffect(() => {
    // Run initial AI analysis on component mount
    runAIAnalysis();
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active": return "bg-blue-100 text-blue-800";
      case "completed": return "bg-green-100 text-green-800";
      case "draft": return "bg-gray-100 text-gray-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getVarianceColor = (variance: number | null) => {
    if (variance === null) return "text-gray-500";
    if (variance === 0) return "text-green-600";
    return "text-red-600";
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'low': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleCreateStockTake = () => {
    if (!newStockTake.name || !newStockTake.startDate || !newStockTake.endDate || !newStockTake.employeeName) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields (Name, Start Date, End Date, Employee Name)",
        variant: "destructive"
      });
      return;
    }

    const newId = Math.max(...stockTakes.map(st => st.id)) + 1;
    const newStockTakeRecord = {
      id: newId,
      name: newStockTake.name,
      status: "draft" as const,
      startDate: newStockTake.startDate,
      endDate: newStockTake.endDate,
      totalItems: 0,
      countedItems: 0,
      progress: 0,
      variances: 0,
      createdBy: newStockTake.employeeName,
      locations: newStockTake.locations.length > 0 ? newStockTake.locations : ["All Locations"],
      description: newStockTake.description || "New stock take operation"
    };

    setStockTakes([...stockTakes, newStockTakeRecord]);
    
    setNewStockTake({
      name: "",
      description: "",
      startDate: "",
      endDate: "",
      employeeName: "",
      locations: [],
      includeCategories: []
    });
    
    setShowCreateModal(false);
    
    toast({
      title: "Stock Take Created",
      description: `"${newStockTake.name}" has been created successfully.`
    });

    console.log("New stock take created:", newStockTakeRecord);
  };

  const handleLocationToggle = (location: string) => {
    setNewStockTake(prev => ({
      ...prev,
      locations: prev.locations.includes(location)
        ? prev.locations.filter(l => l !== location)
        : [...prev.locations, location]
    }));
  };

  const handleCategoryToggle = (category: string) => {
    setNewStockTake(prev => ({
      ...prev,
      includeCategories: prev.includeCategories.includes(category)
        ? prev.includeCategories.filter(c => c !== category)
        : [...prev.includeCategories, category]
    }));
  };

  const handleStartCount = (stockTake: any) => {
    setSelectedStockTake(stockTake);
    console.log("Starting count for:", stockTake.name);
  };

  const handleCountItem = (item: any) => {
    setSelectedCountItem(item);
    setShowCountModal(true);
  };

  const handleSaveCount = (countData: any) => {
    console.log("Saving count:", countData);
    setShowCountModal(false);
    setSelectedCountItem(null);
  };

  const exportCountSheet = () => {
    console.log("Exporting count sheet");
  };

  const handleViewDetails = (stockTake: any) => {
    setSelectedItemForAction(stockTake);
    setShowDetailsModal(true);
    console.log("Viewing details for:", stockTake.name);
  };

  const handleEditStockTake = (stockTake: any) => {
    setSelectedItemForAction(stockTake);
    setShowEditModal(true);
    console.log("Editing stock take:", stockTake.name);
  };

  const handleDeleteStockTake = (stockTake: any) => {
    setSelectedItemForAction(stockTake);
    setShowDeleteModal(true);
    console.log("Delete requested for:", stockTake.name);
  };

  const handleTheftAlert = (stockTake: any) => {
    setSelectedItemForAction(stockTake);
    setShowTheftModal(true);
    console.log("Viewing theft alerts for:", stockTake.name);
  };

  const handleViewNotes = (item: any) => {
    setSelectedItemForAction(item);
    setShowNotesModal(true);
    console.log("Viewing notes for:", item.name);
  };

  const handleViewPhotos = (item: any) => {
    setSelectedItemForAction(item);
    setShowPhotosModal(true);
    console.log("Viewing photos for:", item.name);
  };

  const handleAIAnalysis = (item: any) => {
    setSelectedItemForAction(item);
    setShowAIAnalysisModal(true);
    console.log("Running AI analysis for:", item.name);
  };

  const confirmDelete = () => {
    if (selectedItemForAction) {
      setStockTakes(prev => prev.filter(st => st.id !== selectedItemForAction.id));
      toast({
        title: "Stock Take Deleted",
        description: `"${selectedItemForAction.name}" has been deleted successfully.`
      });
      setShowDeleteModal(false);
      setSelectedItemForAction(null);
    }
  };

  const saveEdit = (updatedData: any) => {
    if (selectedItemForAction) {
      setStockTakes(prev => prev.map(st => 
        st.id === selectedItemForAction.id 
          ? { ...st, ...updatedData }
          : st
      ));
      toast({
        title: "Stock Take Updated",
        description: `"${selectedItemForAction.name}" has been updated successfully.`
      });
      setShowEditModal(false);
      setSelectedItemForAction(null);
    }
  };

  const filteredStockTakes = stockTakes.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === "all" || item.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const totalVariances = countSheetItems.reduce((sum, item) => sum + Math.abs(item.variance || 0), 0);
  const completionRate = countSheetItems.filter(item => item.status === "counted").length / countSheetItems.length * 100;

  return (
    <div className="space-y-6">
      {/* Header with AI Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">AI-Enhanced Stock Take Management</h1>
          <p className="text-gray-500">Intelligent inventory monitoring with real-time theft detection and predictive analytics</p>
        </div>
        <div className="flex space-x-3">
          <Button onClick={runAIAnalysis} disabled={aiAnalysisLoading} className="bg-purple-600 hover:bg-purple-700">
            <Brain className="h-4 w-4 mr-2" />
            {aiAnalysisLoading ? 'Analyzing...' : 'AI Analysis'}
          </Button>
          <Dialog open={showCreateModal} onOpenChange={setShowCreateModal}>
            <DialogTrigger asChild>
              <Button className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-2" />
                New Stock Take
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-lg">
              <DialogHeader>
                <DialogTitle>Create New Stock Take</DialogTitle>
                <DialogDescription>
                  Set up a new inventory counting operation with specific parameters and locations.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 mt-4">
                <div>
                  <Label htmlFor="stocktake-name">Stock Take Name *</Label>
                  <Input
                    id="stocktake-name"
                    placeholder="e.g., Monthly Stock Count - February 2024"
                    value={newStockTake.name}
                    onChange={(e) => setNewStockTake(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                
                <div>
                  <Label htmlFor="employee-name">Employee Name *</Label>
                  <Input
                    id="employee-name"
                    placeholder="Enter employee name responsible for this count"
                    value={newStockTake.employeeName}
                    onChange={(e) => setNewStockTake(prev => ({ ...prev, employeeName: e.target.value }))}
                  />
                </div>
                
                <div>
                  <Label htmlFor="stocktake-description">Description</Label>
                  <Textarea
                    id="stocktake-description"
                    placeholder="Brief description of this stock take operation..."
                    value={newStockTake.description}
                    onChange={(e) => setNewStockTake(prev => ({ ...prev, description: e.target.value }))}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="start-date">Start Date *</Label>
                    <Input
                      id="start-date"
                      type="date"
                      value={newStockTake.startDate}
                      onChange={(e) => setNewStockTake(prev => ({ ...prev, startDate: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="end-date">End Date *</Label>
                    <Input
                      id="end-date"
                      type="date"
                      value={newStockTake.endDate}
                      onChange={(e) => setNewStockTake(prev => ({ ...prev, endDate: e.target.value }))}
                    />
                  </div>
                </div>

                <div>
                  <Label>Locations to Include</Label>
                  <div className="grid grid-cols-2 gap-2 mt-2">
                    {availableLocations.map(location => (
                      <div key={location} className="flex items-center space-x-2">
                        <Checkbox
                          id={`location-${location}`}
                          checked={newStockTake.locations.includes(location)}
                          onCheckedChange={() => handleLocationToggle(location)}
                        />
                        <Label htmlFor={`location-${location}`} className="text-sm">
                          {location}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <Label>Categories to Include</Label>
                  <div className="grid grid-cols-1 gap-2 mt-2">
                    {availableCategories.map(category => (
                      <div key={category} className="flex items-center space-x-2">
                        <Checkbox
                          id={`category-${category}`}
                          checked={newStockTake.includeCategories.includes(category)}
                          onCheckedChange={() => handleCategoryToggle(category)}
                        />
                        <Label htmlFor={`category-${category}`} className="text-sm">
                          {category}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="flex gap-2 pt-4">
                  <Button onClick={handleCreateStockTake} className="flex-1">
                    Create Stock Take
                  </Button>
                  <Button variant="outline" onClick={() => setShowCreateModal(false)}>
                    Cancel
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* AI Insights Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Counts</CardTitle>
            <ClipboardList className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stockTakes.filter(st => st.status === "active").length}</div>
            <p className="text-xs text-muted-foreground">Currently in progress</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Theft Risk Score</CardTitle>
            <Shield className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">6.8/10</div>
            <p className="text-xs text-muted-foreground">{theftAlerts.length} active alerts</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">AI Accuracy</CardTitle>
            <Brain className="h-4 w-4 text-purple-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">92.3%</div>
            <p className="text-xs text-muted-foreground">Prediction confidence</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inventory Health</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">Good</div>
            <p className="text-xs text-muted-foreground">{aiInsights?.overallHealth || 'Analyzing...'}</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content with AI Enhancements */}
      <Tabs defaultValue="dashboard" className="space-y-4">
        <TabsList>
          <TabsTrigger value="dashboard">Stock Take Dashboard</TabsTrigger>
          <TabsTrigger value="counting">Mobile Count Interface</TabsTrigger>
          <TabsTrigger value="variances">Variance Analysis</TabsTrigger>
          <TabsTrigger value="ai-insights">AI Insights</TabsTrigger>
          <TabsTrigger value="theft-detection">Theft Detection</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
              <Input
                placeholder="Search stock takes..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Enhanced Stock Takes Table with AI Alerts */}
          <Card>
            <CardHeader>
              <CardTitle>Stock Take Operations with AI Monitoring</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Stock Take Details</TableHead>
                    <TableHead>Progress</TableHead>
                    <TableHead>Locations</TableHead>
                    <TableHead>Variances</TableHead>
                    <TableHead>AI Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredStockTakes.map((stockTake) => (
                    <TableRow key={stockTake.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{stockTake.name}</p>
                          <p className="text-sm text-gray-500">{stockTake.description}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge className={getStatusColor(stockTake.status)}>
                              {stockTake.status}
                            </Badge>
                            <span className="text-xs text-gray-400">
                              {stockTake.startDate} - {stockTake.endDate}
                            </span>
                          </div>
                          <p className="text-xs text-gray-400 mt-1">Created by {stockTake.createdBy}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            <Progress value={stockTake.progress} className="flex-1" />
                            <span className="text-sm font-medium">{stockTake.progress}%</span>
                          </div>
                          <p className="text-xs text-gray-500">
                            {stockTake.countedItems} / {stockTake.totalItems} items
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {stockTake.locations.map((location, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {location}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {stockTake.variances > 0 && (
                            <AlertTriangle className="h-4 w-4 text-orange-500" />
                          )}
                          <span className={stockTake.variances > 0 ? "text-orange-600 font-medium" : "text-green-600"}>
                            {stockTake.variances} variances
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Brain className="h-4 w-4 text-purple-500" />
                          <Badge variant="outline" className="text-xs">
                            Monitored
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button variant="outline" size="sm" onClick={() => handleViewDetails(stockTake)}>
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handleEditStockTake(stockTake)}>
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm" onClick={exportCountSheet}>
                            <Download className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handleDeleteStockTake(stockTake)} className="hover:bg-red-50 hover:border-red-300">
                            <Trash2 className="h-3 w-3" />
                          </Button>
                          {theftAlerts.some(alert => alert.location?.includes(stockTake.locations[0])) && (
                            <Button variant="outline" size="sm" className="border-red-300 text-red-600" onClick={() => handleTheftAlert(stockTake)}>
                              <Shield className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="counting" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Mobile Count Interface</CardTitle>
              <p className="text-sm text-gray-500">Tablet-optimized interface for warehouse counting</p>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {countSheetItems.map((item) => (
                  <Card key={item.id} className="border-2 hover:border-blue-300 transition-colors">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <h3 className="font-medium text-lg">{item.name}</h3>
                          <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                            <span>SKU: {item.sku}</span>
                            <span>Location: {item.location}</span>
                          </div>
                          <div className="flex items-center gap-4 text-sm text-gray-600 mt-1">
                            <span>Batch: {item.batchNumber}</span>
                            <span>System: {item.systemCount}</span>
                          </div>
                        </div>
                        <Badge variant={item.status === "counted" ? "default" : "secondary"}>
                          {item.status}
                        </Badge>
                      </div>

                      {item.status === "counted" ? (
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Physical Count:</span>
                            <span className="font-medium">{item.physicalCount}</span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Variance:</span>
                            <span className={`font-medium ${getVarianceColor(item.variance)}`}>
                              {item.variance === 0 ? "No variance" : `${item.variance > 0 ? "+" : ""}${item.variance}`}
                            </span>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm">Counted by:</span>
                            <span className="text-sm">{item.countedBy}</span>
                          </div>
                          {item.notes && (
                            <div className="mt-2 p-2 bg-gray-50 rounded text-sm">
                              <strong>Notes:</strong> {item.notes}
                            </div>
                          )}
                          <div className="flex gap-2 mt-3">
                            <Button variant="outline" size="sm" onClick={() => handleCountItem(item)}>
                              <Edit className="h-3 w-3 mr-1" />
                              Edit Count
                            </Button>
                            {item.photos.length > 0 && (
                              <Button variant="outline" size="sm">
                                <Camera className="h-3 w-3 mr-1" />
                                View Photos ({item.photos.length})
                              </Button>
                            )}
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-3">
                          <Button 
                            onClick={() => handleCountItem(item)}
                            className="w-full bg-blue-600 hover:bg-blue-700"
                            size="lg"
                          >
                            <ClipboardList className="h-4 w-4 mr-2" />
                            Start Count
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="variances" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>AI-Enhanced Variance Analysis</CardTitle>
              <p className="text-sm text-gray-500">Color-coded differences with AI-powered insights and theft detection</p>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Item Details</TableHead>
                    <TableHead>System vs Physical</TableHead>
                    <TableHead>Variance</TableHead>
                    <TableHead>AI Analysis</TableHead>
                    <TableHead>Documentation</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {countSheetItems.filter(item => item.variance !== null && item.variance !== 0).map((item) => (
                    <TableRow key={item.id} className="border-l-4 border-l-red-400">
                      <TableCell>
                        <div>
                          <p className="font-medium">{item.name}</p>
                          <p className="text-sm text-gray-500">SKU: {item.sku} | Location: {item.location}</p>
                          <p className="text-sm text-gray-500">Batch: {item.batchNumber}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-600">System:</span>
                            <span className="font-medium">{item.systemCount}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-600">Physical:</span>
                            <span className="font-medium">{item.physicalCount}</span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <AlertTriangle className="h-4 w-4 text-red-500" />
                          <span className="font-medium text-red-600">
                            {item.variance > 0 ? "+" : ""}{item.variance}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <Badge className="bg-purple-100 text-purple-800 border-purple-200">
                            <Brain className="h-3 w-3 mr-1" />
                            AI Flagged
                          </Badge>
                          <p className="text-xs text-gray-600">High deviation detected</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-2">
                          {item.notes && (
                            <div className="flex items-center gap-2">
                              <MessageSquare className="h-4 w-4 text-blue-500" />
                              <span className="text-sm">Notes available</span>
                            </div>
                          )}
                          {item.photos.length > 0 && (
                            <div className="flex items-center gap-2">
                              <Camera className="h-4 w-4 text-green-500" />
                              <span className="text-sm">{item.photos.length} photos</span>
                            </div>
                          )}
                          <p className="text-xs text-gray-500">
                            Counted by {item.countedBy} on {item.countDate}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-1">
                          <Button variant="outline" size="sm" onClick={() => handleViewDetails(item)}>
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handleViewNotes(item)}>
                            <MessageSquare className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => handleViewPhotos(item)}>
                            <Camera className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm" className="border-purple-300 text-purple-600" onClick={() => handleAIAnalysis(item)}>
                            <Brain className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* New AI Insights Tab */}
        <TabsContent value="ai-insights" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-green-500" />
                  Predictive Analytics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {predictions.map((prediction, index) => (
                    <div key={index} className={`p-3 rounded-lg border-l-4 ${
                      prediction.riskLevel === 'high' ? 'border-red-500 bg-red-50' :
                      prediction.riskLevel === 'medium' ? 'border-orange-500 bg-orange-50' :
                      'border-green-500 bg-green-50'
                    }`}>
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">{prediction.itemName}</h4>
                        <Badge className={getSeverityColor(prediction.riskLevel)}>
                          {prediction.riskLevel} risk
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">
                        Predicted Level: {prediction.predictedLevel} units
                      </p>
                      <p className="text-sm text-gray-700">{prediction.recommendations}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Brain className="h-5 w-5 text-purple-500" />
                  Key Insights
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {aiInsights?.keyInsights?.map((insight, index) => (
                    <div key={index} className="p-3 bg-purple-50 rounded-lg border border-purple-200">
                      <p className="text-sm text-purple-800">{insight}</p>
                    </div>
                  ))}
                </div>
                
                <div className="mt-6">
                  <h4 className="font-medium mb-3">AI Recommendations</h4>
                  <div className="space-y-2">
                    {aiInsights?.recommendations?.map((recommendation, index) => (
                      <div key={index} className="flex items-start gap-2">
                        <Zap className="h-4 w-4 text-blue-500 mt-0.5" />
                        <p className="text-sm text-gray-700">{recommendation}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* New Theft Detection Tab */}
        <TabsContent value="theft-detection" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-red-500" />
                Theft Detection Alerts
              </CardTitle>
              <p className="text-sm text-gray-500">AI-powered security monitoring and suspicious pattern detection</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {theftAlerts.map((alert, index) => (
                  <div key={index} className={`p-4 rounded-lg border-l-4 ${getSeverityColor(alert.severity)} border`}>
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Shield className="h-4 w-4" />
                        <h4 className="font-medium">Security Alert</h4>
                      </div>
                      <Badge className={getSeverityColor(alert.severity)}>
                        {alert.severity} priority
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-700 mb-2">{alert.description}</p>
                    <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                      {alert.employee && (
                        <div>
                          <span className="font-medium">Employee:</span> {alert.employee}
                        </div>
                      )}
                      {alert.location && (
                        <div>
                          <span className="font-medium">Location:</span> {alert.location}
                        </div>
                      )}
                    </div>
                    <div className="mt-3 p-2 bg-gray-50 rounded text-sm">
                      <span className="font-medium">Action Required:</span> {alert.actionRequired}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Count Modal */}
      <Dialog open={showCountModal} onOpenChange={setShowCountModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Count Item</DialogTitle>
            <DialogDescription>
              Record the physical count for this inventory item.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {selectedCountItem && (
              <>
                <div>
                  <h3 className="font-medium">{selectedCountItem.name}</h3>
                  <p className="text-sm text-gray-500">SKU: {selectedCountItem.sku}</p>
                  <p className="text-sm text-gray-500">Location: {selectedCountItem.location}</p>
                  <p className="text-sm text-gray-500">System Count: {selectedCountItem.systemCount}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Physical Count</label>
                  <Input type="number" placeholder="Enter counted quantity" />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Notes (Optional)</label>
                  <Textarea placeholder="Add any notes about discrepancies or observations..." />
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="photo" />
                  <label htmlFor="photo" className="text-sm">Add photo documentation</label>
                </div>
                <div className="flex gap-2">
                  <Button onClick={() => handleSaveCount({})} className="flex-1">
                    Save Count
                  </Button>
                  <Button variant="outline" onClick={() => setShowCountModal(false)}>
                    Cancel
                  </Button>
                </div>
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Stock Take Details Modal */}
      <Dialog open={showDetailsModal} onOpenChange={setShowDetailsModal}>
        <DialogContent className="sm:max-w-2xl">
          <DialogHeader>
            <DialogTitle>Stock Take Details</DialogTitle>
          </DialogHeader>
          {selectedItemForAction && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="font-medium">Name</Label>
                  <p className="text-sm text-gray-600">{selectedItemForAction.name}</p>
                </div>
                <div>
                  <Label className="font-medium">Status</Label>
                  <Badge className={getStatusColor(selectedItemForAction.status)}>
                    {selectedItemForAction.status}
                  </Badge>
                </div>
                <div>
                  <Label className="font-medium">Created By</Label>
                  <p className="text-sm text-gray-600">{selectedItemForAction.createdBy}</p>
                </div>
                <div>
                  <Label className="font-medium">Progress</Label>
                  <p className="text-sm text-gray-600">{selectedItemForAction.progress}%</p>
                </div>
              </div>
              <div>
                <Label className="font-medium">Description</Label>
                <p className="text-sm text-gray-600">{selectedItemForAction.description}</p>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Edit Stock Take Modal */}
      <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>Edit Stock Take</DialogTitle>
          </DialogHeader>
          {selectedItemForAction && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-name">Name</Label>
                <Input id="edit-name" defaultValue={selectedItemForAction.name} />
              </div>
              <div>
                <Label htmlFor="edit-description">Description</Label>
                <Textarea id="edit-description" defaultValue={selectedItemForAction.description} />
              </div>
              <div className="flex gap-2">
                <Button onClick={() => saveEdit({ name: "Updated Name" })} className="flex-1">
                  Save Changes
                </Button>
                <Button variant="outline" onClick={() => setShowEditModal(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <Dialog open={showDeleteModal} onOpenChange={setShowDeleteModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this stock take? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          {selectedItemForAction && (
            <div className="space-y-4">
              <div className="p-3 bg-red-50 rounded border border-red-200">
                <p className="font-medium text-red-800">{selectedItemForAction.name}</p>
                <p className="text-sm text-red-600">Created by {selectedItemForAction.createdBy}</p>
              </div>
              <div className="flex gap-2">
                <Button variant="destructive" onClick={confirmDelete} className="flex-1">
                  Delete Stock Take
                </Button>
                <Button variant="outline" onClick={() => setShowDeleteModal(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Theft Alert Modal */}
      <Dialog open={showTheftModal} onOpenChange={setShowTheftModal}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-red-500" />
              Theft Alert Details
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {theftAlerts.map((alert, index) => (
              <div key={index} className="p-3 bg-red-50 rounded border border-red-200">
                <div className="flex items-center justify-between mb-2">
                  <Badge className="bg-red-100 text-red-800">
                    {alert.severity} priority
                  </Badge>
                </div>
                <p className="text-sm text-red-700 mb-2">{alert.description}</p>
                <p className="text-xs text-red-600">{alert.actionRequired}</p>
              </div>
            ))}
          </div>
        </DialogContent>
      </Dialog>

      {/* Notes Modal */}
      <Dialog open={showNotesModal} onOpenChange={setShowNotesModal}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>Item Notes</DialogTitle>
          </DialogHeader>
          {selectedItemForAction && (
            <div className="space-y-4">
              <div className="p-3 bg-gray-50 rounded">
                <h4 className="font-medium">{selectedItemForAction.name}</h4>
                <p className="text-sm text-gray-600 mt-1">
                  {selectedItemForAction.notes || "No notes available for this item."}
                </p>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Photos Modal */}
      <Dialog open={showPhotosModal} onOpenChange={setShowPhotosModal}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>Item Photos</DialogTitle>
          </DialogHeader>
          {selectedItemForAction && (
            <div className="space-y-4">
              <div className="text-center p-8 bg-gray-50 rounded">
                <Camera className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600">
                  {selectedItemForAction.photos?.length > 0 
                    ? `${selectedItemForAction.photos.length} photos available`
                    : "No photos available for this item"
                  }
                </p>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* AI Analysis Modal */}
      <Dialog open={showAIAnalysisModal} onOpenChange={setShowAIAnalysisModal}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5 text-purple-500" />
              AI Analysis Results
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="p-3 bg-purple-50 rounded border border-purple-200">
              <h4 className="font-medium text-purple-800 mb-2">Analysis Summary</h4>
              <p className="text-sm text-purple-700">
                AI has detected unusual variance patterns for this item. 
                Variance exceeds normal threshold by 150%.
              </p>
            </div>
            <div className="p-3 bg-orange-50 rounded border border-orange-200">
              <h4 className="font-medium text-orange-800 mb-2">Recommended Actions</h4>
              <ul className="text-sm text-orange-700 space-y-1">
                <li>• Perform immediate recount</li>
                <li>• Verify receiving records</li>
                <li>• Check for damaged inventory</li>
              </ul>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default StockTake;
