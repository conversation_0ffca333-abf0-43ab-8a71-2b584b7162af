import { useState, useEffect } from "react";
import { Package, Search, Plus, Edit, AlertTriangle, Settings, Filter } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useBusinessConfig } from "@/hooks/useBusinessConfig";
import { useProductTerminology, useInventoryTerminology } from "@/contexts/TerminologyContext";
import { CategoryManager } from "@/components/CategoryManager";
import { AttributeManager } from "@/components/AttributeManager";

const FlexibleInventory = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [showLowStock, setShowLowStock] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);

  const { categories, attributes, products, isLoading } = useBusinessConfig();
  const productTerms = useProductTerminology();
  const inventoryTerms = useInventoryTerminology();

  // Mock products data - in real app this would come from the database
  const [mockProducts] = useState([
    {
      id: "1",
      name: "Sample Product 1",
      sku: "SP001",
      categoryId: categories[0]?.id || null,
      stockQuantity: 25,
      minStockLevel: 10,
      price: 99.99,
      cost: 75.00,
      attributes: {
        brand: "Sample Brand",
        color: "Blue",
        size: "Medium"
      }
    },
    {
      id: "2", 
      name: "Sample Product 2",
      sku: "SP002",
      categoryId: categories[1]?.id || null,
      stockQuantity: 5,
      minStockLevel: 15,
      price: 149.99,
      cost: 110.00,
      attributes: {
        brand: "Premium Brand",
        color: "Red",
        size: "Large"
      }
    }
  ]);

  const filteredProducts = mockProducts.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.sku.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = categoryFilter === "all" || product.categoryId === categoryFilter;
    const matchesLowStock = !showLowStock || product.stockQuantity <= product.minStockLevel;
    
    return matchesSearch && matchesCategory && matchesLowStock;
  });

  const lowStockCount = mockProducts.filter(p => p.stockQuantity <= p.minStockLevel).length;
  const totalValue = mockProducts.reduce((sum, p) => sum + (p.stockQuantity * p.cost), 0);

  const getCategoryName = (categoryId: string | null) => {
    if (!categoryId) return 'Uncategorized';
    const category = categories.find(c => c.id === categoryId);
    return category?.name || 'Unknown';
  };

  const getCategoryColor = (categoryId: string | null) => {
    if (!categoryId) return 'bg-gray-500';
    const category = categories.find(c => c.id === categoryId);
    return category?.color || 'bg-gray-500';
  };

  const renderAttributeValue = (product: any, attributeName: string) => {
    const attribute = attributes.find(attr => attr.name === attributeName);
    const value = product.attributes?.[attributeName];
    
    if (!value || !attribute) return '-';
    
    if (attribute.type === 'boolean') {
      return value ? 'Yes' : 'No';
    }
    
    return value;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading {inventoryTerms.stockLower}...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">{inventoryTerms.stock} Management</h1>
          <p className="text-gray-600">Manage your {productTerms.pluralLower} and {inventoryTerms.stockLower} levels</p>
        </div>
        <div className="flex gap-2">
          <CategoryManager 
            trigger={
              <Button variant="outline">
                <Settings className="h-4 w-4 mr-2" />
                Manage Categories
              </Button>
            }
          />
          <AttributeManager 
            trigger={
              <Button variant="outline">
                <Settings className="h-4 w-4 mr-2" />
                Manage Attributes
              </Button>
            }
          />
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Add {productTerms.singular}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total {productTerms.plural}</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockProducts.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Low {inventoryTerms.stock}</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{lowStockCount}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Categories</CardTitle>
            <Filter className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{categories.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalValue.toFixed(2)}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder={`Search ${productTerms.pluralLower}...`}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button
              variant={showLowStock ? "default" : "outline"}
              onClick={() => setShowLowStock(!showLowStock)}
            >
              <AlertTriangle className="h-4 w-4 mr-2" />
              Low {inventoryTerms.stock}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Products Table */}
      <Card>
        <CardHeader>
          <CardTitle>{productTerms.plural} ({filteredProducts.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>{productTerms.singular} Name</TableHead>
                <TableHead>SKU</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>{inventoryTerms.stock}</TableHead>
                <TableHead>Price</TableHead>
                {attributes.slice(0, 3).map((attr) => (
                  <TableHead key={attr.id}>{attr.label}</TableHead>
                ))}
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredProducts.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7 + attributes.slice(0, 3).length} className="text-center py-8">
                    <div className="text-gray-500">
                      <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No {productTerms.pluralLower} found</p>
                      <p className="text-sm">Try adjusting your search or filters</p>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                filteredProducts.map((product) => (
                  <TableRow key={product.id}>
                    <TableCell className="font-medium">{product.name}</TableCell>
                    <TableCell>{product.sku}</TableCell>
                    <TableCell>
                      <Badge 
                        className={`${getCategoryColor(product.categoryId)} text-white`}
                      >
                        {getCategoryName(product.categoryId)}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className={product.stockQuantity <= product.minStockLevel ? 'text-orange-600 font-semibold' : ''}>
                          {product.stockQuantity}
                        </span>
                        {product.stockQuantity <= product.minStockLevel && (
                          <AlertTriangle className="h-4 w-4 text-orange-500" />
                        )}
                      </div>
                    </TableCell>
                    <TableCell>${product.price.toFixed(2)}</TableCell>
                    {attributes.slice(0, 3).map((attr) => (
                      <TableCell key={attr.id}>
                        {renderAttributeValue(product, attr.name)}
                      </TableCell>
                    ))}
                    <TableCell>
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default FlexibleInventory;
