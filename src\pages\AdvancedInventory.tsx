
import { useState, useEffect } from "react";
import { Package, Search, Plus, Edit, AlertTriangle, Battery, Truck, ArrowRightLeft, FileText, Barcode, AlertCircle, Calendar, TrendingUp, Award, Clock } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import SerialNumberModal from "@/components/inventory/SerialNumberModal";
import WarrantyModal from "@/components/inventory/WarrantyModal";
import SupplierAnalyticsModal from "@/components/inventory/SupplierAnalyticsModal";

const AdvancedInventory = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [showSerialModal, setShowSerialModal] = useState(false);
  const [showWarrantyModal, setShowWarrantyModal] = useState(false);
  const [showAnalyticsModal, setShowAnalyticsModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState(null);

  // Enhanced inventory with serial numbers and warranty tracking
  const [inventory, setInventory] = useState([
    {
      id: 1,
      name: "12V Deep Cycle AGM Battery",
      sku: "AGM-12V-100",
      category: "Deep Cycle",
      currentStock: 15,
      minStock: 5,
      reorderLevel: 8,
      leadTimeDays: 14,
      avgMonthlySales: 5,
      totalSold: 45,
      serialNumbers: ["AGM001", "AGM002", "AGM003"],
      warrantyMonths: 24,
      supplierId: "SUP001",
      supplierName: "Battery World",
      supplierRating: 4.5,
      avgDeliveryTime: 12,
      lastOrderDate: "2024-01-15",
      costPrice: 95.00,
      sellingPrice: 129.99,
      profitMargin: 27.0
    },
    {
      id: 2,
      name: "Car Battery 550CCA",
      sku: "CAR-550",
      category: "Automotive",
      currentStock: 2,
      minStock: 5,
      reorderLevel: 7,
      leadTimeDays: 10,
      avgMonthlySales: 8,
      totalSold: 32,
      serialNumbers: ["CAR001", "CAR002"],
      warrantyMonths: 12,
      supplierId: "SUP002",
      supplierName: "AutoParts Inc",
      supplierRating: 4.2,
      avgDeliveryTime: 9,
      lastOrderDate: "2024-01-10",
      costPrice: 65.00,
      sellingPrice: 89.99,
      profitMargin: 27.8
    }
  ]);

  const [autoReorders, setAutoReorders] = useState([]);
  const [warrantyExpiring, setWarrantyExpiring] = useState([]);

  useEffect(() => {
    // Calculate auto reorders needed
    const needsReorder = inventory.filter(item => {
      const projectedStock = item.currentStock - (item.avgMonthlySales * (item.leadTimeDays / 30));
      return projectedStock <= item.reorderLevel;
    });
    setAutoReorders(needsReorder);

    // Find warranties expiring in next 90 days
    const expiringSoon = inventory.filter(item => {
      // Simulate warranty expiry dates
      const warrantyExpiry = new Date();
      warrantyExpiry.setMonth(warrantyExpiry.getMonth() + 3);
      return true; // For demo purposes
    });
    setWarrantyExpiring(expiringSoon.slice(0, 2));
  }, [inventory]);

  const handleAutoReorder = (item) => {
    const suggestedQuantity = Math.max(item.minStock * 2, item.avgMonthlySales * 2);
    console.log(`Auto-reordering ${suggestedQuantity} units of ${item.name}`);
    // Here you would typically create a purchase order
  };

  const categories = ["all", "Deep Cycle", "Automotive", "Marine", "Motorcycle"];

  const filteredInventory = inventory.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.sku.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = categoryFilter === "all" || item.category === categoryFilter;
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Advanced Inventory Management</h1>
          <p className="text-gray-500">Serial tracking, warranty management, and supplier analytics</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setShowAnalyticsModal(true)} variant="outline">
            <TrendingUp className="h-4 w-4 mr-2" />
            Supplier Analytics
          </Button>
          <Button onClick={() => setShowWarrantyModal(true)} variant="outline">
            <Award className="h-4 w-4 mr-2" />
            Warranty Manager
          </Button>
          <Button onClick={() => setShowSerialModal(true)} className="bg-blue-600 hover:bg-blue-700">
            <Barcode className="h-4 w-4 mr-2" />
            Serial Numbers
          </Button>
        </div>
      </div>

      {/* Auto Reorder Alerts */}
      {autoReorders.length > 0 && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader className="pb-3">
            <CardTitle className="text-blue-800 flex items-center gap-2">
              <Truck className="h-5 w-5" />
              Auto Reorder Suggestions ({autoReorders.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {autoReorders.map((item) => (
                <div key={item.id} className="flex items-center justify-between p-3 bg-white rounded-lg">
                  <div>
                    <p className="font-medium text-blue-900">{item.name}</p>
                    <p className="text-sm text-blue-700">
                      Current: {item.currentStock} | Projected need: {Math.max(item.minStock * 2, item.avgMonthlySales * 2)} units
                    </p>
                  </div>
                  <Button size="sm" onClick={() => handleAutoReorder(item)}>
                    Auto Reorder
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Serial Numbers Tracked</CardTitle>
            <Barcode className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {inventory.reduce((sum, item) => sum + item.serialNumbers.length, 0)}
            </div>
            <p className="text-xs text-muted-foreground">Individual units tracked</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Warranties Expiring</CardTitle>
            <Clock className="h-4 w-4 text-orange-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{warrantyExpiring.length}</div>
            <p className="text-xs text-muted-foreground">Next 90 days</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Supplier Rating</CardTitle>
            <Award className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {(inventory.reduce((sum, item) => sum + item.supplierRating, 0) / inventory.length).toFixed(1)}
            </div>
            <p className="text-xs text-muted-foreground">Out of 5.0</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Auto Reorders Due</CardTitle>
            <TrendingUp className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{autoReorders.length}</div>
            <p className="text-xs text-muted-foreground">Based on sales velocity</p>
          </CardContent>
        </Card>
      </div>

      {/* Tabs for different views */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="serial">Serial Numbers</TabsTrigger>
          <TabsTrigger value="warranty">Warranty Tracking</TabsTrigger>
          <TabsTrigger value="supplier">Supplier Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-4">
            <div className="relative flex-1">
              <Search className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
              <Input
                placeholder="Search by product name or SKU..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category === "all" ? "All Categories" : category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Enhanced Inventory Table */}
          <Card>
            <CardHeader>
              <CardTitle>Advanced Inventory Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Product Details</TableHead>
                      <TableHead>Stock & Reorder</TableHead>
                      <TableHead>Sales Analytics</TableHead>
                      <TableHead>Supplier Performance</TableHead>
                      <TableHead>Profitability</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredInventory.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{item.name}</p>
                            <p className="text-sm text-gray-500">{item.sku}</p>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="outline" className="text-xs">
                                {item.serialNumbers.length} tracked
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                {item.warrantyMonths}mo warranty
                              </Badge>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <Badge variant={item.currentStock <= item.reorderLevel ? "destructive" : "default"}>
                                {item.currentStock}
                              </Badge>
                              <span className="text-sm text-gray-500">units</span>
                            </div>
                            <p className="text-xs text-gray-400">
                              Reorder at: {item.reorderLevel} | Lead: {item.leadTimeDays}d
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <p className="text-sm font-medium">{item.avgMonthlySales}/month avg</p>
                            <p className="text-xs text-gray-500">Total sold: {item.totalSold}</p>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div 
                                className="bg-blue-600 h-2 rounded-full" 
                                style={{ width: `${Math.min((item.currentStock / (item.avgMonthlySales * 3)) * 100, 100)}%` }}
                              ></div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <p className="text-sm font-medium">{item.supplierName}</p>
                            <div className="flex items-center gap-1">
                              <div className="flex">
                                {[...Array(5)].map((_, i) => (
                                  <span key={i} className={`text-xs ${i < Math.floor(item.supplierRating) ? 'text-yellow-500' : 'text-gray-300'}`}>★</span>
                                ))}
                              </div>
                              <span className="text-xs text-gray-500">({item.supplierRating})</span>
                            </div>
                            <p className="text-xs text-gray-400">Avg delivery: {item.avgDeliveryTime}d</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <p className="text-sm font-medium">${item.sellingPrice}</p>
                            <p className="text-xs text-gray-500">Cost: ${item.costPrice}</p>
                            <Badge variant="outline" className={`text-xs ${item.profitMargin > 25 ? 'text-green-600' : 'text-orange-600'}`}>
                              {item.profitMargin}% margin
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col gap-1">
                            <Button variant="outline" size="sm" onClick={() => { setSelectedItem(item); setShowSerialModal(true); }}>
                              <Barcode className="h-3 w-3 mr-1" />
                              Serials
                            </Button>
                            <Button variant="outline" size="sm" onClick={() => { setSelectedItem(item); setShowWarrantyModal(true); }}>
                              <Award className="h-3 w-3 mr-1" />
                              Warranty
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="serial">
          <Card>
            <CardHeader>
              <CardTitle>Serial Number Management</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500 mb-4">Track individual units with serial numbers for warranty and service history.</p>
              <Button onClick={() => setShowSerialModal(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Serial Numbers
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="warranty">
          <Card>
            <CardHeader>
              <CardTitle>Warranty Management</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500 mb-4">Monitor warranty periods and get alerts for expiring warranties.</p>
              <Button onClick={() => setShowWarrantyModal(true)}>
                <Calendar className="h-4 w-4 mr-2" />
                Manage Warranties
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="supplier">
          <Card>
            <CardHeader>
              <CardTitle>Supplier Performance Analytics</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500 mb-4">Analyze supplier performance, delivery times, and quality ratings.</p>
              <Button onClick={() => setShowAnalyticsModal(true)}>
                <TrendingUp className="h-4 w-4 mr-2" />
                View Analytics
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Modals */}
      <SerialNumberModal
        isOpen={showSerialModal}
        onClose={() => setShowSerialModal(false)}
        selectedItem={selectedItem}
      />

      <WarrantyModal
        isOpen={showWarrantyModal}
        onClose={() => setShowWarrantyModal(false)}
        selectedItem={selectedItem}
      />

      <SupplierAnalyticsModal
        isOpen={showAnalyticsModal}
        onClose={() => setShowAnalyticsModal(false)}
      />
    </div>
  );
};

export default AdvancedInventory;
