
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FileText, Plus, Trash2 } from "lucide-react";

interface PurchaseOrderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreatePO: (data: any) => void;
}

const PurchaseOrderModal = ({ isOpen, onClose, onCreatePO }: PurchaseOrderModalProps) => {
  const [poData, setPOData] = useState({
    supplierId: "",
    expectedDate: "",
    notes: ""
  });

  const [items, setItems] = useState([
    { productId: "", quantity: "", unitPrice: "" }
  ]);

  const suppliers = [
    { id: "1", name: "Battery World" },
    { id: "2", name: "AutoParts Inc" },
    { id: "3", name: "Marine Supply Co" }
  ];

  const products = [
    { id: "1", name: "12V Deep Cycle AGM Battery" },
    { id: "2", name: "Car Battery 550CCA" },
    { id: "3", name: "Marine Battery 105Ah" }
  ];

  const addItem = () => {
    setItems([...items, { productId: "", quantity: "", unitPrice: "" }]);
  };

  const removeItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index));
  };

  const updateItem = (index: number, field: string, value: string) => {
    const updatedItems = items.map((item, i) => 
      i === index ? { ...item, [field]: value } : item
    );
    setItems(updatedItems);
  };

  const handleSubmit = () => {
    const poNumber = `PO-${Date.now().toString().slice(-6)}`;
    onCreatePO({
      ...poData,
      poNumber,
      items,
      status: "pending",
      createdDate: new Date().toISOString()
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Create Purchase Order
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="supplier">Supplier</Label>
              <Select value={poData.supplierId} onValueChange={(value) => setPOData({...poData, supplierId: value})}>
                <SelectTrigger>
                  <SelectValue placeholder="Select supplier" />
                </SelectTrigger>
                <SelectContent>
                  {suppliers.map((supplier) => (
                    <SelectItem key={supplier.id} value={supplier.id}>
                      {supplier.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="expectedDate">Expected Delivery</Label>
              <Input
                id="expectedDate"
                type="date"
                value={poData.expectedDate}
                onChange={(e) => setPOData({...poData, expectedDate: e.target.value})}
              />
            </div>
          </div>

          <div>
            <Label htmlFor="notes">Notes</Label>
            <Input
              id="notes"
              value={poData.notes}
              onChange={(e) => setPOData({...poData, notes: e.target.value})}
              placeholder="Additional notes or requirements"
            />
          </div>

          <div>
            <div className="flex items-center justify-between mb-3">
              <Label>Order Items</Label>
              <Button type="button" onClick={addItem} size="sm" variant="outline">
                <Plus className="h-4 w-4 mr-1" />
                Add Item
              </Button>
            </div>

            <div className="space-y-3">
              {items.map((item, index) => (
                <div key={index} className="grid grid-cols-12 gap-2 items-end">
                  <div className="col-span-5">
                    <Select value={item.productId} onValueChange={(value) => updateItem(index, 'productId', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select product" />
                      </SelectTrigger>
                      <SelectContent>
                        {products.map((product) => (
                          <SelectItem key={product.id} value={product.id}>
                            {product.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="col-span-2">
                    <Input
                      type="number"
                      placeholder="Qty"
                      value={item.quantity}
                      onChange={(e) => updateItem(index, 'quantity', e.target.value)}
                    />
                  </div>
                  <div className="col-span-3">
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="Unit Price"
                      value={item.unitPrice}
                      onChange={(e) => updateItem(index, 'unitPrice', e.target.value)}
                    />
                  </div>
                  <div className="col-span-2">
                    <Button
                      type="button"
                      onClick={() => removeItem(index)}
                      size="sm"
                      variant="outline"
                      disabled={items.length === 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="flex gap-2 pt-4">
            <Button variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
            <Button onClick={handleSubmit} className="flex-1">
              Create Purchase Order
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PurchaseOrderModal;
