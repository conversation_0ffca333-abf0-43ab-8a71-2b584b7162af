import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { useBusinessConfig } from '@/hooks/useBusinessConfig';
import { ProductAttribute, AttributeType } from '@/types/business';
import { Plus, Edit, Trash2, Settings, X } from 'lucide-react';

interface AttributeManagerProps {
  trigger?: React.ReactNode;
}

const ATTRIBUTE_TYPES: { value: AttributeType; label: string; description: string }[] = [
  { value: 'text', label: 'Text', description: 'Single line text input' },
  { value: 'textarea', label: 'Long Text', description: 'Multi-line text area' },
  { value: 'number', label: 'Number', description: 'Numeric input' },
  { value: 'select', label: 'Dropdown', description: 'Single selection from options' },
  { value: 'multiselect', label: 'Multi-Select', description: 'Multiple selections from options' },
  { value: 'boolean', label: 'Yes/No', description: 'True/false checkbox' },
  { value: 'date', label: 'Date', description: 'Date picker' },
  { value: 'url', label: 'URL', description: 'Web address input' },
  { value: 'email', label: 'Email', description: 'Email address input' }
];

export const AttributeManager = ({ trigger }: AttributeManagerProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isAddingAttribute, setIsAddingAttribute] = useState(false);
  const [editingAttribute, setEditingAttribute] = useState<ProductAttribute | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    label: '',
    type: 'text' as AttributeType,
    options: [] as string[],
    newOption: '',
    isRequired: false
  });

  const { attributes, createAttribute, config, terminology } = useBusinessConfig();
  const { toast } = useToast();

  const resetForm = () => {
    setFormData({
      name: '',
      label: '',
      type: 'text',
      options: [],
      newOption: '',
      isRequired: false
    });
    setEditingAttribute(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim() || !formData.label.trim()) {
      toast({
        title: 'Required Fields Missing',
        description: 'Please enter both name and label for the attribute.',
        variant: 'destructive'
      });
      return;
    }

    if ((formData.type === 'select' || formData.type === 'multiselect') && formData.options.length === 0) {
      toast({
        title: 'Options Required',
        description: 'Please add at least one option for dropdown/multi-select attributes.',
        variant: 'destructive'
      });
      return;
    }

    if (!config) {
      toast({
        title: 'Configuration Error',
        description: 'Business configuration not found.',
        variant: 'destructive'
      });
      return;
    }

    try {
      const attributeData = {
        businessId: config.id,
        name: formData.name.trim().toLowerCase().replace(/\s+/g, '_'),
        label: formData.label.trim(),
        type: formData.type,
        options: (formData.type === 'select' || formData.type === 'multiselect') ? formData.options : undefined,
        isRequired: formData.isRequired,
        sortOrder: attributes.length + 1,
        isActive: true
      };

      await createAttribute(attributeData);
      
      toast({
        title: 'Attribute Created',
        description: `${formData.label} has been added successfully.`
      });

      resetForm();
      setIsAddingAttribute(false);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to create attribute.',
        variant: 'destructive'
      });
    }
  };

  const addOption = () => {
    if (formData.newOption.trim() && !formData.options.includes(formData.newOption.trim())) {
      setFormData(prev => ({
        ...prev,
        options: [...prev.options, prev.newOption.trim()],
        newOption: ''
      }));
    }
  };

  const removeOption = (index: number) => {
    setFormData(prev => ({
      ...prev,
      options: prev.options.filter((_, i) => i !== index)
    }));
  };

  const handleEdit = (attribute: ProductAttribute) => {
    setFormData({
      name: attribute.name,
      label: attribute.label,
      type: attribute.type,
      options: attribute.options || [],
      newOption: '',
      isRequired: attribute.isRequired
    });
    setEditingAttribute(attribute);
    setIsAddingAttribute(true);
  };

  const handleCancel = () => {
    resetForm();
    setIsAddingAttribute(false);
  };

  const needsOptions = formData.type === 'select' || formData.type === 'multiselect';

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline">
            <Settings className="h-4 w-4 mr-2" />
            Manage Attributes
          </Button>
        )}
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            {terminology.product.singular} Attribute Management
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Add Attribute Form */}
          {isAddingAttribute && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {editingAttribute ? 'Edit' : 'Add New'} Attribute
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="label">Display Label *</Label>
                      <Input
                        id="label"
                        value={formData.label}
                        onChange={(e) => setFormData(prev => ({ ...prev, label: e.target.value }))}
                        placeholder="e.g., Brand, Color, Size"
                      />
                    </div>
                    <div>
                      <Label htmlFor="name">Internal Name *</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="e.g., brand, color, size"
                      />
                      <p className="text-xs text-gray-500 mt-1">Used for data storage (lowercase, no spaces)</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="type">Attribute Type *</Label>
                      <Select value={formData.type} onValueChange={(value: AttributeType) => setFormData(prev => ({ ...prev, type: value }))}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {ATTRIBUTE_TYPES.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              <div>
                                <div className="font-medium">{type.label}</div>
                                <div className="text-xs text-gray-500">{type.description}</div>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex items-center space-x-2 pt-6">
                      <Switch
                        id="required"
                        checked={formData.isRequired}
                        onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isRequired: checked }))}
                      />
                      <Label htmlFor="required">Required field</Label>
                    </div>
                  </div>

                  {/* Options for select/multiselect */}
                  {needsOptions && (
                    <div>
                      <Label>Options</Label>
                      <div className="space-y-2">
                        <div className="flex gap-2">
                          <Input
                            value={formData.newOption}
                            onChange={(e) => setFormData(prev => ({ ...prev, newOption: e.target.value }))}
                            placeholder="Add option"
                            onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addOption())}
                          />
                          <Button type="button" onClick={addOption}>
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {formData.options.map((option, index) => (
                            <Badge key={index} variant="secondary" className="flex items-center gap-1">
                              {option}
                              <button
                                type="button"
                                onClick={() => removeOption(index)}
                                className="ml-1 hover:text-red-600"
                              >
                                <X className="h-3 w-3" />
                              </button>
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="flex justify-end gap-2">
                    <Button type="button" variant="outline" onClick={handleCancel}>
                      Cancel
                    </Button>
                    <Button type="submit">
                      {editingAttribute ? 'Update' : 'Create'} Attribute
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          )}

          {/* Attributes List */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">
                Current Attributes ({attributes.length})
              </h3>
              {!isAddingAttribute && (
                <Button onClick={() => setIsAddingAttribute(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Attribute
                </Button>
              )}
            </div>

            {attributes.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No attributes created yet.</p>
                <p className="text-sm">Add your first attribute to customize {terminology.product.plural.toLowerCase()}.</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {attributes.map((attribute) => (
                  <Card key={attribute.id}>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h4 className="font-semibold">{attribute.label}</h4>
                          <p className="text-sm text-gray-600">{attribute.name}</p>
                        </div>
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(attribute)}
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      
                      <div className="flex flex-wrap gap-2 mb-2">
                        <Badge variant="outline" className="text-xs">
                          {ATTRIBUTE_TYPES.find(t => t.value === attribute.type)?.label}
                        </Badge>
                        {attribute.isRequired && (
                          <Badge variant="destructive" className="text-xs">Required</Badge>
                        )}
                        <Badge variant="secondary" className="text-xs">
                          Order: {attribute.sortOrder}
                        </Badge>
                      </div>

                      {attribute.options && attribute.options.length > 0 && (
                        <div className="text-xs text-gray-600">
                          Options: {attribute.options.slice(0, 3).join(', ')}
                          {attribute.options.length > 3 && ` +${attribute.options.length - 3} more`}
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
