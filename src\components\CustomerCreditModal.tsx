
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CreditCard, DollarSign, Calendar, AlertTriangle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Customer {
  id: number;
  name: string;
  email: string;
  creditLimit: number;
  currentBalance: number;
  paymentTerms: number;
  creditStatus: string;
  lastPayment: string;
  totalSpent: number;
}

interface CustomerCreditModalProps {
  isOpen: boolean;
  onClose: () => void;
  customer: Customer | null;
  onSave: (creditData: Partial<Customer>) => void;
}

const CustomerCreditModal = ({ isOpen, onClose, customer, onSave }: CustomerCreditModalProps) => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    creditLimit: 0,
    paymentTerms: 30,
    creditStatus: "good",
  });

  useEffect(() => {
    if (customer) {
      setFormData({
        creditLimit: customer.creditLimit,
        paymentTerms: customer.paymentTerms,
        creditStatus: customer.creditStatus,
      });
    }
  }, [customer]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.creditLimit < 0) {
      toast({
        title: "Validation Error",
        description: "Credit limit cannot be negative.",
        variant: "destructive",
      });
      return;
    }

    onSave(formData);
    onClose();
  };

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const getCreditUtilization = () => {
    if (!customer || customer.creditLimit === 0) return 0;
    return Math.round((customer.currentBalance / customer.creditLimit) * 100);
  };

  const getAgingDays = () => {
    if (!customer) return 0;
    const today = new Date();
    const paymentDate = new Date(customer.lastPayment);
    const diffTime = Math.abs(today.getTime() - paymentDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  if (!customer) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Credit Management - {customer.name}</DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Current Balance</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${customer.currentBalance.toLocaleString()}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Credit Utilization</CardTitle>
              <CreditCard className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{getCreditUtilization()}%</div>
              <Badge variant={getCreditUtilization() > 80 ? "destructive" : "default"} className="text-xs">
                {getCreditUtilization() > 80 ? "High" : "Normal"}
              </Badge>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Days Since Payment</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{getAgingDays()}</div>
              <Badge variant={getAgingDays() > customer.paymentTerms ? "destructive" : "default"} className="text-xs">
                {getAgingDays() > customer.paymentTerms ? "Overdue" : "Current"}
              </Badge>
            </CardContent>
          </Card>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="creditLimit">Credit Limit ($)</Label>
              <Input
                id="creditLimit"
                type="number"
                min="0"
                step="100"
                value={formData.creditLimit}
                onChange={(e) => handleInputChange("creditLimit", Number(e.target.value))}
              />
              <p className="text-xs text-muted-foreground mt-1">
                Available: ${Math.max(0, formData.creditLimit - customer.currentBalance).toLocaleString()}
              </p>
            </div>
            
            <div>
              <Label htmlFor="paymentTerms">Payment Terms (Days)</Label>
              <Select 
                value={formData.paymentTerms.toString()} 
                onValueChange={(value) => handleInputChange("paymentTerms", Number(value))}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="15">15 Days</SelectItem>
                  <SelectItem value="30">30 Days</SelectItem>
                  <SelectItem value="45">45 Days</SelectItem>
                  <SelectItem value="60">60 Days</SelectItem>
                  <SelectItem value="90">90 Days</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="creditStatus">Credit Status</Label>
            <Select 
              value={formData.creditStatus} 
              onValueChange={(value) => handleInputChange("creditStatus", value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="excellent">Excellent</SelectItem>
                <SelectItem value="good">Good</SelectItem>
                <SelectItem value="fair">Fair</SelectItem>
                <SelectItem value="poor">Poor</SelectItem>
                <SelectItem value="warning">Warning</SelectItem>
                <SelectItem value="overdue">Overdue</SelectItem>
                <SelectItem value="suspended">Suspended</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {customer.currentBalance > customer.creditLimit && (
            <div className="flex items-center space-x-2 p-4 bg-red-50 border border-red-200 rounded-lg">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              <div>
                <p className="text-sm font-medium text-red-800">Credit Limit Exceeded</p>
                <p className="text-sm text-red-600">
                  Current balance (${customer.currentBalance.toLocaleString()}) exceeds credit limit.
                </p>
              </div>
            </div>
          )}

          <div className="bg-gray-50 p-4 rounded-lg">
            <h3 className="font-medium mb-2">Customer Summary</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Total Spent:</span>
                <span className="ml-2 font-medium">${customer.totalSpent.toLocaleString()}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Last Payment:</span>
                <span className="ml-2 font-medium">{customer.lastPayment}</span>
              </div>
            </div>
          </div>
          
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">
              Update Credit Settings
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CustomerCreditModal;
