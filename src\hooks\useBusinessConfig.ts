import { useState, useEffect, useCallback } from 'react';
import { 
  BusinessConfiguration, 
  ProductCategory, 
  ProductAttribute, 
  BusinessTerminology 
} from '@/types/business';
import { businessConfigService } from '@/services/businessConfigService';

export interface UseBusinessConfigReturn {
  // Configuration
  config: BusinessConfiguration | null;
  isConfigured: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Categories
  categories: ProductCategory[];
  
  // Attributes
  attributes: ProductAttribute[];
  
  // Terminology
  terminology: BusinessTerminology;
  
  // Actions
  initializeFromTemplate: (businessName: string, templateId: string) => Promise<void>;
  refreshConfig: () => Promise<void>;
  createCategory: (category: Omit<ProductCategory, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  createAttribute: (attribute: Omit<ProductAttribute, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
}

export const useBusinessConfig = (): UseBusinessConfigReturn => {
  const [config, setConfig] = useState<BusinessConfiguration | null>(null);
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [attributes, setAttributes] = useState<ProductAttribute[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load initial configuration
  const loadConfig = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [configData, categoriesData, attributesData] = await Promise.all([
        businessConfigService.getCurrentConfig(),
        businessConfigService.getCategories(),
        businessConfigService.getAttributes()
      ]);

      setConfig(configData);
      setCategories(categoriesData || []);
      setAttributes(attributesData || []);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load configuration');
      console.error('Error loading business configuration:', err);
      // Set empty arrays as fallback
      setCategories([]);
      setAttributes([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initialize from template
  const initializeFromTemplate = useCallback(async (businessName: string, templateId: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const newConfig = await businessConfigService.initializeFromTemplate(businessName, templateId);
      
      // Reload all data
      const [categoriesData, attributesData] = await Promise.all([
        businessConfigService.getCategories(),
        businessConfigService.getAttributes()
      ]);

      setConfig(newConfig);
      setCategories(categoriesData);
      setAttributes(attributesData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to initialize from template');
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Refresh configuration
  const refreshConfig = useCallback(async () => {
    await loadConfig();
  }, [loadConfig]);

  // Create category
  const createCategory = useCallback(async (categoryData: Omit<ProductCategory, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const newCategory = await businessConfigService.createCategory(categoryData);
      setCategories(prev => [...prev, newCategory].sort((a, b) => a.sortOrder - b.sortOrder));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create category');
      throw err;
    }
  }, []);

  // Create attribute
  const createAttribute = useCallback(async (attributeData: Omit<ProductAttribute, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const newAttribute = await businessConfigService.createAttribute(attributeData);
      setAttributes(prev => [...prev, newAttribute].sort((a, b) => a.sortOrder - b.sortOrder));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create attribute');
      throw err;
    }
  }, []);

  // Get terminology with fallback
  const terminology = businessConfigService.getTerminology() || {
    product: { singular: 'Product', plural: 'Products', singularLower: 'product', pluralLower: 'products' },
    inventory: { stock: 'Inventory', stockLower: 'inventory' },
    sales: { order: 'Order', orderPlural: 'Orders', transaction: 'Sale', transactionPlural: 'Sales' },
    custom: {}
  };

  // Load configuration on mount
  useEffect(() => {
    loadConfig();
  }, [loadConfig]);

  return {
    config,
    isConfigured: !!config,
    isLoading,
    error,
    categories,
    attributes,
    terminology,
    initializeFromTemplate,
    refreshConfig,
    createCategory,
    createAttribute
  };
};
