
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { LineChart, Line, ResponsiveContainer, XAxis, YAxis, CartesianGrid, <PERSON>ltip, Legend } from "recharts";
import { Brain, TrendingUp, RefreshCw, Zap } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { AIService } from "./AIService";

interface AIForecastingPanelProps {
  historicalData: any[];
}

export const AIForecastingPanel = ({ historicalData }: AIForecastingPanelProps) => {
  const [forecast, setForecast] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const aiService = new AIService();

  const generateForecast = async () => {
    setIsLoading(true);
    try {
      const result = await aiService.generateForecast(historicalData);
      setForecast(result);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to generate forecast:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    generateForecast();
  }, []);

  const combinedData = [...historicalData, ...(forecast?.predictions || [])];

  return (
    <Card className="col-span-2">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <Brain className="h-5 w-5 mr-2 text-purple-600" />
            AI Revenue Forecasting
          </CardTitle>
          <div className="flex items-center gap-2">
            {forecast && (
              <Badge variant="outline" className="text-green-600">
                <Zap className="h-3 w-3 mr-1" />
                {forecast.accuracy}% Accuracy
              </Badge>
            )}
            <Button 
              onClick={generateForecast} 
              disabled={isLoading}
              size="sm"
              variant="outline"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              {isLoading ? 'Generating...' : 'Regenerate'}
            </Button>
          </div>
        </div>
        {lastUpdated && (
          <p className="text-sm text-gray-500">
            Last updated: {lastUpdated.toLocaleString()}
          </p>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={combinedData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value, name) => [`$${value?.toLocaleString()}`, name]} />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="actual" 
                stroke="#22c55e" 
                strokeWidth={2} 
                name="Actual Revenue" 
              />
              <Line 
                type="monotone" 
                dataKey="forecast" 
                stroke="#3b82f6" 
                strokeWidth={2} 
                strokeDasharray="5 5" 
                name="Traditional Forecast" 
              />
              <Line 
                type="monotone" 
                dataKey="forecast" 
                stroke="#8b5cf6" 
                strokeWidth={3} 
                name="AI Forecast" 
              />
            </LineChart>
          </ResponsiveContainer>

          {forecast && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {forecast.predictions.map((prediction: any, index: number) => (
                <div key={index} className="bg-gradient-to-r from-purple-50 to-blue-50 p-4 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-purple-800">{prediction.month}</h4>
                    <TrendingUp className={`h-4 w-4 ${prediction.trend === 'up' ? 'text-green-600' : 'text-red-600'}`} />
                  </div>
                  <p className="text-2xl font-bold text-purple-900">
                    ${prediction.forecast.toLocaleString()}
                  </p>
                  <div className="mt-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Confidence</span>
                      <span>{prediction.confidence}%</span>
                    </div>
                    <Progress value={prediction.confidence} className="mt-1" />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
