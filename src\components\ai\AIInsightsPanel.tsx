
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Lightbulb, AlertTriangle, TrendingUp, RefreshCw, Target, DollarSign, Package } from "lucide-react";
import { AIService } from "./AIService";

interface AIInsightsPanelProps {
  kpiData: any[];
}

export const AIInsightsPanel = ({ kpiData }: AIInsightsPanelProps) => {
  const [insights, setInsights] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const aiService = new AIService();

  const generateInsights = async () => {
    setIsLoading(true);
    try {
      const result = await aiService.generateInsights(kpiData);
      setInsights(result);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to generate insights:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (kpiData.length > 0) {
      generateInsights();
    }
  }, [kpiData]);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'revenue': return <DollarSign className="h-4 w-4" />;
      case 'inventory': return <Package className="h-4 w-4" />;
      case 'marketing': return <Target className="h-4 w-4" />;
      default: return <TrendingUp className="h-4 w-4" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center">
            <Lightbulb className="h-5 w-5 mr-2 text-yellow-600" />
            AI Insights
          </CardTitle>
          <Button 
            onClick={generateInsights} 
            disabled={isLoading}
            size="sm"
            variant="outline"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? 'Analyzing...' : 'Refresh'}
          </Button>
        </div>
        {lastUpdated && (
          <p className="text-sm text-gray-500">
            Last analyzed: {lastUpdated.toLocaleString()}
          </p>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {insights.length === 0 && !isLoading ? (
            <div className="text-center py-8 text-gray-500">
              <Lightbulb className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No insights available. Click refresh to generate AI insights.</p>
            </div>
          ) : (
            insights.map((insight, index) => (
              <div key={index} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    {getCategoryIcon(insight.category)}
                    <Badge className={getPriorityColor(insight.priority)}>
                      {insight.priority.toUpperCase()}
                    </Badge>
                  </div>
                  <AlertTriangle className={`h-4 w-4 ${
                    insight.priority === 'high' ? 'text-red-500' : 
                    insight.priority === 'medium' ? 'text-yellow-500' : 'text-green-500'
                  }`} />
                </div>
                
                <div className="space-y-2">
                  <p className="text-sm font-medium text-gray-900">
                    {insight.insight}
                  </p>
                  <div className="bg-blue-50 p-3 rounded-md">
                    <p className="text-sm text-blue-800">
                      <strong>Recommended Action:</strong> {insight.action}
                    </p>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {insights.length > 0 && (
          <div className="mt-6 p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <TrendingUp className="h-5 w-5 text-purple-600" />
              <h4 className="font-semibold text-purple-800">Overall Business Health</h4>
            </div>
            <p className="text-sm text-purple-700">
              Based on current metrics, your business shows strong growth momentum with some optimization opportunities. 
              Focus on inventory management and continue leveraging your successful customer acquisition strategy.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
