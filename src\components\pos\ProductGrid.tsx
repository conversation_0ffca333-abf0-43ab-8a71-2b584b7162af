
import { Plus } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface Product {
  id: number;
  name: string;
  price: number;
  sku: string;
  barcode: string;
  category: string;
  inStock: number;
}

interface ProductGridProps {
  filteredProducts: Product[];
  onAddToCart: (product: Product) => void;
}

export const ProductGrid = ({ filteredProducts, onAddToCart }: ProductGridProps) => {
  return (
    <div className="flex-1 p-4 overflow-y-auto">
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        {filteredProducts.map((product) => (
          <Card 
            key={product.id} 
            className="cursor-pointer hover:shadow-lg transition-shadow border-2 hover:border-blue-200"
            onClick={() => onAddToCart(product)}
          >
            <CardContent className="p-4">
              <div className="flex justify-between items-start mb-2">
                <Badge variant={product.inStock > 5 ? "default" : "destructive"} className="text-xs">
                  {product.inStock} left
                </Badge>
              </div>
              <h3 className="font-semibold text-sm mb-1 line-clamp-2">{product.name}</h3>
              <p className="text-xs text-gray-500 mb-2">{product.sku}</p>
              <div className="flex justify-between items-center">
                <span className="text-lg font-bold text-green-600">${product.price}</span>
                <Button size="sm" className="h-8 w-8 p-0">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
