
import { CreditCard, Receipt, Calculator, Percent } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface Customer {
  id: number;
  name: string;
  phone: string;
  email: string;
  discount: number;
}

interface CheckoutSectionProps {
  subtotal: number;
  customerDiscount: number;
  manualDiscount: number;
  tax: number;
  total: number;
  discountPercent: number;
  setDiscountPercent: (percent: number) => void;
  selectedCustomer: Customer | null;
  paymentMethod: string;
  setPaymentMethod: (method: string) => void;
  onCheckout: () => void;
  onHoldTransaction: () => void;
  onGenerateQuote: () => void;
}

export const CheckoutSection = ({
  subtotal,
  customerDiscount,
  manualDiscount,
  tax,
  total,
  discountPercent,
  setDiscountPercent,
  selectedCustomer,
  paymentMethod,
  setPaymentMethod,
  onCheckout,
  onHoldTransaction,
  onGenerateQuote,
}: CheckoutSectionProps) => {
  const totalDiscount = customerDiscount + manualDiscount;

  return (
    <div className="border-t p-4 space-y-4">
      {/* Discount Section */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm">Discount %:</span>
          <div className="flex items-center space-x-2">
            <Input
              type="number"
              value={discountPercent}
              onChange={(e) => setDiscountPercent(Number(e.target.value))}
              className="w-16 h-8 text-center"
              min="0"
              max="100"
            />
            <Percent className="h-4 w-4 text-gray-400" />
          </div>
        </div>
        {selectedCustomer && (
          <p className="text-xs text-blue-600">Customer discount: {selectedCustomer.discount}%</p>
        )}
      </div>

      {/* Totals */}
      <div className="space-y-2 text-sm">
        <div className="flex justify-between">
          <span>Subtotal:</span>
          <span>${subtotal.toFixed(2)}</span>
        </div>
        {totalDiscount > 0 && (
          <div className="flex justify-between text-red-600">
            <span>Discount:</span>
            <span>-${totalDiscount.toFixed(2)}</span>
          </div>
        )}
        <div className="flex justify-between">
          <span>Tax (8%):</span>
          <span>${tax.toFixed(2)}</span>
        </div>
        <hr />
        <div className="flex justify-between text-lg font-bold">
          <span>Total:</span>
          <span className="text-green-600">${total.toFixed(2)}</span>
        </div>
      </div>

      {/* Payment Method */}
      <Tabs value={paymentMethod} onValueChange={setPaymentMethod}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="cash">Cash</TabsTrigger>
          <TabsTrigger value="card">Card</TabsTrigger>
          <TabsTrigger value="credit">Credit</TabsTrigger>
        </TabsList>
      </Tabs>

      {/* Action Buttons */}
      <div className="space-y-2">
        <Button 
          className="w-full bg-green-600 hover:bg-green-700 text-lg py-6"
          onClick={onCheckout}
        >
          <CreditCard className="h-5 w-5 mr-2" />
          Process Payment
        </Button>
        <div className="grid grid-cols-2 gap-2">
          <Button variant="outline" size="sm" onClick={onHoldTransaction}>
            <Receipt className="h-4 w-4 mr-1" />
            Hold
          </Button>
          <Button variant="outline" size="sm" onClick={onGenerateQuote}>
            <Calculator className="h-4 w-4 mr-1" />
            Quote
          </Button>
        </div>
      </div>
    </div>
  );
};
