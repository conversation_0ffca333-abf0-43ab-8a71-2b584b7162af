
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Layout from "./components/Layout";
import LandingPage from "./pages/LandingPage";
import Dashboard from "./pages/Dashboard";
import POS from "./pages/POS";
import Inventory from "./pages/Inventory";
import StockTake from "./pages/StockTake";
import ReturnsManagement from "./pages/ReturnsManagement";
import Customers from "./pages/Customers";
import Reports from "./pages/Reports";
import FinancialReports from "./pages/FinancialReports";
import BusinessIntelligence from "./pages/BusinessIntelligence";
import Settings from "./pages/Settings";
import NotFound from "./pages/NotFound";
import AdvancedInventory from "./pages/AdvancedInventory";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<LandingPage />} />
          <Route path="/dashboard" element={<Layout><Dashboard /></Layout>} />
          <Route path="/pos" element={<Layout><POS /></Layout>} />
          <Route path="/inventory" element={<Layout><Inventory /></Layout>} />
          <Route path="/advanced-inventory" element={<Layout><AdvancedInventory /></Layout>} />
          <Route path="/stock-take" element={<Layout><StockTake /></Layout>} />
          <Route path="/returns" element={<Layout><ReturnsManagement /></Layout>} />
          <Route path="/customers" element={<Layout><Customers /></Layout>} />
          <Route path="/reports" element={<Layout><Reports /></Layout>} />
          <Route path="/financial-reports" element={<Layout><FinancialReports /></Layout>} />
          <Route path="/business-intelligence" element={<Layout><BusinessIntelligence /></Layout>} />
          <Route path="/settings" element={<Layout><Settings /></Layout>} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
