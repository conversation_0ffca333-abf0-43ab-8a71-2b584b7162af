
import { useState, useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Layout from "./components/Layout";
import LandingPage from "./pages/LandingPage";
import Dashboard from "./pages/Dashboard";
import POS from "./pages/POS";
import Inventory from "./pages/Inventory";
import StockTake from "./pages/StockTake";
import ReturnsManagement from "./pages/ReturnsManagement";
import Customers from "./pages/Customers";
import Reports from "./pages/Reports";
import FinancialReports from "./pages/FinancialReports";
import BusinessIntelligence from "./pages/BusinessIntelligence";
import Settings from "./pages/Settings";
import NotFound from "./pages/NotFound";
import AdvancedInventory from "./pages/AdvancedInventory";
import { BusinessSetupWizard } from "@/components/BusinessSetupWizard";
import { useBusinessConfig } from "@/hooks/useBusinessConfig";
import { TerminologyProvider } from "@/contexts/TerminologyContext";

const queryClient = new QueryClient();

const AppContent = () => {
  const { isConfigured, isLoading, initializeFromTemplate } = useBusinessConfig();
  const [showSetupWizard, setShowSetupWizard] = useState(false);

  useEffect(() => {
    if (!isLoading && !isConfigured) {
      setShowSetupWizard(true);
    }
  }, [isLoading, isConfigured]);

  const handleSetupComplete = async (businessName: string, templateId: string) => {
    await initializeFromTemplate(businessName, templateId);
    setShowSetupWizard(false);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your business configuration...</p>
        </div>
      </div>
    );
  }

  return (
    <TerminologyProvider>
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<LandingPage />} />
          <Route path="/dashboard" element={<Layout><Dashboard /></Layout>} />
          <Route path="/pos" element={<Layout><POS /></Layout>} />
          <Route path="/inventory" element={<Layout><Inventory /></Layout>} />
          <Route path="/advanced-inventory" element={<Layout><AdvancedInventory /></Layout>} />
          <Route path="/stock-take" element={<Layout><StockTake /></Layout>} />
          <Route path="/returns" element={<Layout><ReturnsManagement /></Layout>} />
          <Route path="/customers" element={<Layout><Customers /></Layout>} />
          <Route path="/reports" element={<Layout><Reports /></Layout>} />
          <Route path="/financial-reports" element={<Layout><FinancialReports /></Layout>} />
          <Route path="/business-intelligence" element={<Layout><BusinessIntelligence /></Layout>} />
          <Route path="/settings" element={<Layout><Settings /></Layout>} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>

      <BusinessSetupWizard
        isOpen={showSetupWizard}
        onClose={() => setShowSetupWizard(false)}
        onComplete={handleSetupComplete}
      />
    </TerminologyProvider>
  );
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <AppContent />
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
