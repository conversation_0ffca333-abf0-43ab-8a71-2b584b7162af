
import React from 'react';
import { ArrowRight, Store, BarChart3, Users, Shield, Zap, CheckCircle, Star, Play, Brain, Bot, TrendingUp, Lightbulb, AlertTriangle, Eye, Target } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Link } from 'react-router-dom';

const LandingPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <header className="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-slate-200/60">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center">
              <Store className="w-6 h-6 text-white" />
            </div>
            <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Business Hub
            </span>
          </div>
          <Link to="/pos">
            <Button className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-6 py-2 rounded-full transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
              Launch App <ArrowRight className="ml-2 w-4 h-4" />
            </Button>
          </Link>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative overflow-hidden py-20 px-4">
        <div className="container mx-auto text-center relative z-10">
          <div className="animate-fade-in">
            <Badge className="mb-6 bg-gradient-to-r from-purple-100 to-blue-100 text-purple-800 border-purple-200 px-4 py-2 text-sm font-medium">
              🤖 AI-Powered Business Management with Smart Operations
            </Badge>
            <h1 className="text-5xl md:text-7xl font-black mb-6 bg-gradient-to-r from-slate-900 via-blue-900 to-indigo-900 bg-clip-text text-transparent leading-tight">
              Revolutionary
              <br />
              <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                AI Business Hub
              </span>
              <br />
              Management
            </h1>
            <p className="text-xl md:text-2xl text-slate-600 mb-8 max-w-3xl mx-auto leading-relaxed">
              Transform your business with our AI-powered management system. 
              Get intelligent forecasts, automated theft detection, real-time inventory monitoring, and smart recommendations to optimize your operations and boost profits.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link to="/pos">
                <Button size="lg" className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 hover:scale-105 shadow-xl hover:shadow-2xl">
                  Start Free Trial <ArrowRight className="ml-2 w-5 h-5" />
                </Button>
              </Link>
              <Button variant="outline" size="lg" className="border-2 border-slate-300 hover:border-blue-500 px-8 py-4 rounded-full text-lg font-semibold transition-all duration-300 hover:scale-105">
                <Play className="mr-2 w-5 h-5" />
                Watch Demo
              </Button>
            </div>
          </div>
        </div>
        
        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-full opacity-20 animate-pulse"></div>
        <div className="absolute top-40 right-20 w-32 h-32 bg-gradient-to-br from-indigo-400 to-purple-500 rounded-full opacity-10 animate-bounce"></div>
        <div className="absolute bottom-20 left-1/4 w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full opacity-15 animate-pulse"></div>
      </section>

      {/* AI Features Highlight Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-purple-600 to-blue-700">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
            Powered by Artificial Intelligence
          </h2>
          <p className="text-xl text-purple-100 mb-12 max-w-3xl mx-auto">
            Our advanced AI engine analyzes your business data to provide actionable insights, accurate forecasts, intelligent theft detection, and automated inventory monitoring.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <div className="w-14 h-14 bg-gradient-to-br from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center mb-4 mx-auto">
                <Brain className="w-7 h-7 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-3">AI Revenue Forecasting</h3>
              <p className="text-purple-100 text-sm">
                Get accurate revenue predictions with 94%+ accuracy using advanced machine learning
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <div className="w-14 h-14 bg-gradient-to-br from-red-400 to-orange-500 rounded-2xl flex items-center justify-center mb-4 mx-auto">
                <AlertTriangle className="w-7 h-7 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-3">Smart Theft Detection</h3>
              <p className="text-purple-100 text-sm">
                Real-time anomaly detection identifies suspicious patterns and potential theft across your business
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <div className="w-14 h-14 bg-gradient-to-br from-green-400 to-emerald-500 rounded-2xl flex items-center justify-center mb-4 mx-auto">
                <Eye className="w-7 h-7 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-3">Real-Time Monitoring</h3>
              <p className="text-purple-100 text-sm">
                Continuous monitoring with instant alerts for variances and irregularities across all operations
              </p>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <div className="w-14 h-14 bg-gradient-to-br from-blue-400 to-cyan-500 rounded-2xl flex items-center justify-center mb-4 mx-auto">
                <Target className="w-7 h-7 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-3">Predictive Analytics</h3>
              <p className="text-purple-100 text-sm">
                Forecast stock levels and prevent shortages with intelligent trend analysis
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 px-4 bg-white">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-slate-900 to-blue-900 bg-clip-text text-transparent">
              Powerful Features for Modern Business
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              Everything you need to manage your business efficiently and profitably with advanced AI protection
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                icon: <Zap className="w-8 h-8 text-yellow-500" />,
                title: "Smart POS System",
                description: "Lightning-fast point of sale with barcode scanning, customer management, and real-time inventory updates.",
                gradient: "from-yellow-100 to-orange-100"
              },
              {
                icon: <BarChart3 className="w-8 h-8 text-blue-500" />,
                title: "Advanced Analytics",
                description: "Comprehensive business intelligence with sales reports, inventory analytics, and profit tracking.",
                gradient: "from-blue-100 to-indigo-100"
              },
              {
                icon: <BarChart3 className="w-8 h-8 text-green-500" />,
                title: "AI-Enhanced Inventory Control",
                description: "Complete inventory management with AI-powered theft detection, anomaly monitoring, and predictive analytics.",
                gradient: "from-green-100 to-emerald-100"
              },
              {
                icon: <Users className="w-8 h-8 text-purple-500" />,
                title: "Customer Management",
                description: "Build lasting relationships with customer profiles, purchase history, and loyalty programs.",
                gradient: "from-purple-100 to-pink-100"
              },
              {
                icon: <Shield className="w-8 h-8 text-red-500" />,
                title: "Smart Security Monitoring",
                description: "AI-powered theft prevention with employee pattern analysis and comprehensive security alerts.",
                gradient: "from-red-100 to-rose-100"
              },
              {
                icon: <CheckCircle className="w-8 h-8 text-teal-500" />,
                title: "Intelligent Inventory Management",
                description: "AI-enhanced inventory tracking with variance analysis, real-time monitoring, and automated reorder points.",
                gradient: "from-teal-100 to-cyan-100"
              }
            ].map((feature, index) => (
              <Card key={index} className="group hover:shadow-xl transition-all duration-500 hover:scale-105 border-0 shadow-lg bg-gradient-to-br from-white to-slate-50">
                <CardHeader>
                  <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${feature.gradient} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                    {feature.icon}
                  </div>
                  <CardTitle className="text-xl font-bold text-slate-900">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-slate-600 text-base leading-relaxed">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-blue-600 to-indigo-700">
        <div className="container mx-auto text-center">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {[
              { number: "10K+", label: "Happy Customers" },
              { number: "97%", label: "Theft Detection Rate" },
              { number: "24/7", label: "AI Monitoring" },
              { number: "50+", label: "Countries" }
            ].map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl md:text-5xl font-black text-white mb-2">{stat.number}</div>
                <div className="text-blue-100 text-lg font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 px-4 bg-slate-50">
        <div className="container mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-slate-900 to-blue-900 bg-clip-text text-transparent">
              What Our Customers Say
            </h2>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[
              {
                name: "Sarah Johnson",
                role: "Store Manager",
                company: "TechRetail Solutions",
                text: "The AI theft detection caught discrepancies we never noticed before! Our inventory shrinkage dropped by 85% in just 3 months. The predictive analytics are game-changing.",
                rating: 5
              },
              {
                name: "Mike Chen",
                role: "CEO",
                company: "RetailTech Inc",
                text: "Real-time monitoring gives us complete visibility. The AI flagged suspicious patterns that led us to discover internal theft worth $50K. This system paid for itself immediately.",
                rating: 5
              },
              {
                name: "Emma Rodriguez",
                role: "Operations Director",
                company: "Retail World",
                text: "The inventory automation with AI analysis is incredible. We now catch variances instantly and our accuracy is 99.2%. Staff accountability has never been better.",
                rating: 5
              }
            ].map((testimonial, index) => (
              <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-white">
                <CardContent className="p-8">
                  <div className="flex mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <p className="text-slate-700 mb-6 text-lg leading-relaxed">"{testimonial.text}"</p>
                  <div>
                    <div className="font-bold text-slate-900">{testimonial.name}</div>
                    <div className="text-slate-600">{testimonial.role}</div>
                    <div className="text-blue-600 font-medium">{testimonial.company}</div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900">
        <div className="container mx-auto text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
            Ready to Secure Your Inventory with AI?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join thousands of businesses that trust Business Hub's AI-powered platform to prevent theft, optimize operations, and maximize profits with intelligent monitoring
          </p>
          <Link to="/pos">
            <Button size="lg" className="bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white px-10 py-4 rounded-full text-xl font-bold transition-all duration-300 hover:scale-105 shadow-2xl">
              Get Started Now <ArrowRight className="ml-2 w-6 h-6" />
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 text-white py-12 px-4">
        <div className="container mx-auto text-center">
          <div className="flex items-center justify-center space-x-3 mb-6">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
              <Store className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold">Business Hub Manager</span>
          </div>
          <p className="text-slate-400 mb-4">© 2024 Business Hub Manager. All rights reserved.</p>
          <p className="text-slate-500">Powering the future of business management with AI-enhanced security and intelligence</p>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
