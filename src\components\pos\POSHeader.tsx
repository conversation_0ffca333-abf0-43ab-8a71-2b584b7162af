
import { useState } from "react";
import { Search, User, Barcode } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON><PERSON><PERSON>le, DialogTrigger, DialogDescription } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";

interface Customer {
  id: number;
  name: string;
  phone: string;
  email: string;
  discount: number;
}

interface POSHeaderProps {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  selectedCustomer: Customer | null;
  onCustomerSelect: (customer: Customer) => void;
  onProductScan: (code: string) => void;
}

const customers = [
  { id: 1, name: "<PERSON>", phone: "(*************", email: "<EMAIL>", discount: 5 },
  { id: 2, name: "ABC Auto Shop", phone: "(*************", email: "<EMAIL>", discount: 10 },
  { id: 3, name: "<PERSON>", phone: "(*************", email: "<EMAIL>", discount: 0 },
  { id: 4, name: "City Garage", phone: "(*************", email: "<EMAIL>", discount: 15 },
];

export const POSHeader = ({ 
  searchQuery, 
  setSearchQuery, 
  selectedCustomer, 
  onCustomerSelect, 
  onProductScan 
}: POSHeaderProps) => {
  const [isCustomerDialogOpen, setIsCustomerDialogOpen] = useState(false);
  const [isScanDialogOpen, setIsScanDialogOpen] = useState(false);
  const [scannedCode, setScannedCode] = useState("");
  const { toast } = useToast();

  const handleScanSubmit = () => {
    if (scannedCode) {
      onProductScan(scannedCode);
      setScannedCode("");
      setIsScanDialogOpen(false);
    }
  };

  const handleSelectCustomer = (customer: Customer) => {
    onCustomerSelect(customer);
    setIsCustomerDialogOpen(false);
    toast({
      title: "Customer Selected",
      description: `${customer.name} has been selected`,
    });
  };

  return (
    <div className="p-4 border-b bg-white">
      <div className="flex items-center justify-between mb-4">
        <h1 className="text-2xl font-bold text-gray-900">Point of Sale</h1>
        <div className="flex items-center space-x-2">
          <Dialog open={isScanDialogOpen} onOpenChange={setIsScanDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <Barcode className="h-4 w-4 mr-2" />
                Scan
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Scan Barcode</DialogTitle>
                <DialogDescription>
                  Enter or scan a product barcode/SKU to add it to the cart
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="barcode">Barcode/SKU</Label>
                  <Input
                    id="barcode"
                    value={scannedCode}
                    onChange={(e) => setScannedCode(e.target.value)}
                    placeholder="Enter barcode or SKU..."
                    onKeyPress={(e) => e.key === 'Enter' && handleScanSubmit()}
                  />
                </div>
                <Button onClick={handleScanSubmit} className="w-full">
                  Add to Cart
                </Button>
              </div>
            </DialogContent>
          </Dialog>

          <Dialog open={isCustomerDialogOpen} onOpenChange={setIsCustomerDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <User className="h-4 w-4 mr-2" />
                {selectedCustomer ? selectedCustomer.name : "Select Customer"}
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Select Customer</DialogTitle>
                <DialogDescription>
                  Choose a customer for this transaction
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {customers.map((customer) => (
                  <div
                    key={customer.id}
                    className="p-3 border rounded-lg cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSelectCustomer(customer)}
                  >
                    <div className="font-medium">{customer.name}</div>
                    <div className="text-sm text-gray-500">{customer.phone}</div>
                    <div className="text-sm text-gray-500">{customer.email}</div>
                    {customer.discount > 0 && (
                      <Badge variant="secondary" className="mt-1">
                        {customer.discount}% discount
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>
      
      <div className="relative">
        <Search className="h-5 w-5 absolute left-3 top-3 text-gray-400" />
        <Input
          placeholder="Search by name, SKU, or barcode..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10 text-lg py-6"
        />
      </div>
    </div>
  );
};
