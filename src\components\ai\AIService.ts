
import { supabase } from "@/integrations/supabase/client";

// AI Service for handling various AI operations through Supabase Edge Functions
export class AIService {
  constructor() {
    // No API key needed - handled securely by Edge Function
  }

  async generateForecast(historicalData: any[]): Promise<any> {
    try {
      console.log('Calling AI edge function for forecast');
      
      const { data, error } = await supabase.functions.invoke('deepseek-ai', {
        body: {
          action: 'forecast',
          payload: { historicalData }
        }
      });

      if (error) {
        console.error('Edge function error:', error);
        throw new Error('Failed to generate forecast');
      }

      return data;
    } catch (error) {
      console.error('Forecast generation error:', error);
      // Return mock data for demonstration
      return this.getMockForecast();
    }
  }

  async chatQuery(query: string, dashboardData: any): Promise<string> {
    try {
      console.log('Calling AI edge function for chat');
      
      const { data, error } = await supabase.functions.invoke('deepseek-ai', {
        body: {
          action: 'chat',
          payload: { query, dashboardData }
        }
      });

      if (error) {
        console.error('Edge function error:', error);
        throw new Error('Failed to process query');
      }

      return data;
    } catch (error) {
      console.error('Chat query error:', error);
      return "I'm having trouble accessing the AI service right now. Please try again later.";
    }
  }

  async generateInsights(kpiData: any[]): Promise<any[]> {
    try {
      console.log('Calling AI edge function for insights');
      
      const { data, error } = await supabase.functions.invoke('deepseek-ai', {
        body: {
          action: 'insights',
          payload: { kpiData }
        }
      });

      if (error) {
        console.error('Edge function error:', error);
        throw new Error('Failed to generate insights');
      }

      return Array.isArray(data) ? data : [data];
    } catch (error) {
      console.error('Insights generation error:', error);
      return this.getMockInsights();
    }
  }

  private getMockForecast(): any {
    return {
      predictions: [
        { month: 'Jul', forecast: 63000, confidence: 82, trend: 'up' },
        { month: 'Aug', forecast: 66000, confidence: 78, trend: 'up' },
        { month: 'Sep', forecast: 69000, confidence: 75, trend: 'up' },
      ],
      accuracy: 94.2,
      model: 'AI Forecast (Fallback)'
    };
  }

  private getMockInsights(): any[] {
    return [
      {
        insight: "Revenue growth of 23.5% exceeds target by 3.5%. Consider scaling marketing efforts.",
        priority: "high",
        action: "Increase marketing budget by 15% to capitalize on positive momentum",
        category: "revenue"
      },
      {
        insight: "Inventory turnover at 6.8x is below target 8x. Optimize stock levels.",
        priority: "medium",
        action: "Reduce slow-moving inventory by 20% and improve demand forecasting",
        category: "inventory"
      },
      {
        insight: "Customer acquisition cost is well below target. Opportunity for growth.",
        priority: "low",
        action: "Consider increasing customer acquisition investment",
        category: "marketing"
      }
    ];
  }
}
