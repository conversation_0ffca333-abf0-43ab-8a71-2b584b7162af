
import { Battery, ShoppingCart, Package, TrendingUp, AlertTriangle, DollarSign, Users, Clock, Target, Zap, Truck, CreditCard } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { LineChart, Line, AreaChart, Area, BarChart, Bar, ResponsiveContainer, XAxis, YAxis, CartesianGrid, Tooltip, Legend, PieChart, Pie, Cell } from "recharts";

const Dashboard = () => {
  const { toast } = useToast();

  // Mock comprehensive business data
  const businessMetrics = {
    todaySales: 2450.00,
    todayTransactions: 18,
    monthlyRevenue: 45750.00,
    monthlyTarget: 50000.00,
    lowStockItems: 8,
    totalProducts: 156,
    totalCustomers: 342,
    newCustomersThisMonth: 28,
    pendingOrders: 5,
    avgOrderValue: 136.11,
    conversionRate: 23.5,
    topSellingCategory: "12V Deep Cycle",
    profitMargin: 32.8,
    inventoryTurnover: 4.2
  };

  const salesData = [
    { day: 'Mon', sales: 1200, orders: 8, customers: 12 },
    { day: 'Tue', sales: 1800, orders: 12, customers: 18 },
    { day: 'Wed', sales: 2200, orders: 15, customers: 22 },
    { day: 'Thu', sales: 1600, orders: 10, customers: 16 },
    { day: 'Fri', sales: 2800, orders: 18, customers: 28 },
    { day: 'Sat', sales: 3200, orders: 22, customers: 32 },
    { day: 'Sun', sales: 2100, orders: 14, customers: 20 }
  ];

  const monthlyTrend = [
    { month: 'Jan', revenue: 38000, profit: 12500, orders: 156 },
    { month: 'Feb', revenue: 42000, profit: 14200, orders: 178 },
    { month: 'Mar', revenue: 39500, profit: 13100, orders: 162 },
    { month: 'Apr', revenue: 44500, profit: 15200, orders: 189 },
    { month: 'May', revenue: 47200, profit: 16800, orders: 201 },
    { month: 'Jun', revenue: 45750, profit: 15900, orders: 194 }
  ];

  const productCategoryData = [
    { name: '12V Deep Cycle', value: 35, color: '#3b82f6' },
    { name: 'Car Batteries', value: 28, color: '#10b981' },
    { name: 'Marine Batteries', value: 20, color: '#f59e0b' },
    { name: 'Motorcycle', value: 12, color: '#ef4444' },
    { name: 'Industrial', value: 5, color: '#8b5cf6' }
  ];

  const recentActivities = [
    { id: 1, type: 'sale', customer: 'John Doe', product: '12V Deep Cycle Battery', amount: 129.99, time: '2 mins ago', status: 'completed' },
    { id: 2, type: 'order', customer: 'Jane Smith', product: 'Car Battery 550CCA', amount: 89.99, time: '15 mins ago', status: 'processing' },
    { id: 3, type: 'restock', supplier: 'Battery Plus Inc', product: 'Marine Battery', quantity: 25, time: '32 mins ago', status: 'received' },
    { id: 4, type: 'customer', customer: 'Mike Johnson', action: 'New Registration', time: '1 hour ago', status: 'active' },
    { id: 5, type: 'alert', message: 'Low stock: Motorcycle Battery', time: '2 hours ago', status: 'warning' }
  ];

  const topProducts = [
    { name: '12V AGM Deep Cycle', sold: 45, revenue: 5850, trend: '+12%' },
    { name: 'Car Battery 650CCA', sold: 38, revenue: 3420, trend: '+8%' },
    { name: 'Marine Starting Battery', sold: 32, revenue: 6400, trend: '+15%' },
    { name: 'Motorcycle Battery', sold: 28, revenue: 1960, trend: '-3%' },
    { name: 'Industrial Deep Cycle', sold: 22, revenue: 5500, trend: '+25%' }
  ];

  const lowStockAlerts = [
    { name: "12V AGM Deep Cycle", currentStock: 2, minStock: 5, severity: 'critical' },
    { name: "Car Battery 650CCA", currentStock: 1, minStock: 3, severity: 'critical' },
    { name: "Motorcycle Battery", currentStock: 3, minStock: 5, severity: 'warning' },
    { name: "Marine Starting Battery", currentStock: 4, minStock: 8, severity: 'warning' },
  ];

  const handleQuickAction = (action: string) => {
    toast({
      title: `${action} Initiated`,
      description: `${action} process has been started successfully.`,
    });
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return 'bg-green-500';
    if (percentage >= 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'sale': return <DollarSign className="h-4 w-4 text-green-600" />;
      case 'order': return <ShoppingCart className="h-4 w-4 text-blue-600" />;
      case 'restock': return <Package className="h-4 w-4 text-purple-600" />;
      case 'customer': return <Users className="h-4 w-4 text-indigo-600" />;
      case 'alert': return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      default: return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with Quick Actions */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Business Dashboard</h1>
          <p className="text-gray-500">Complete overview of your battery center operations</p>
        </div>
        <div className="flex space-x-3">
          <Link to="/pos">
            <Button className="bg-blue-600 hover:bg-blue-700">
              <ShoppingCart className="h-4 w-4 mr-2" />
              Quick Sale
            </Button>
          </Link>
          <Button onClick={() => handleQuickAction('Inventory Check')} variant="outline">
            <Package className="h-4 w-4 mr-2" />
            Stock Take
          </Button>
        </div>
      </div>

      {/* Key Performance Indicators */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-800">Today's Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-900">${businessMetrics.todaySales.toFixed(2)}</div>
            <p className="text-xs text-blue-600">+12% from yesterday • {businessMetrics.todayTransactions} transactions</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-800">Monthly Progress</CardTitle>
            <Target className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-900">
              ${businessMetrics.monthlyRevenue.toLocaleString()}
            </div>
            <div className="mt-2">
              <Progress value={(businessMetrics.monthlyRevenue / businessMetrics.monthlyTarget) * 100} className="h-2" />
              <p className="text-xs text-green-600 mt-1">
                {Math.round((businessMetrics.monthlyRevenue / businessMetrics.monthlyTarget) * 100)}% of ${businessMetrics.monthlyTarget.toLocaleString()} target
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-orange-50 to-orange-100 border-orange-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-800">Inventory Alerts</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-900">{businessMetrics.lowStockItems}</div>
            <p className="text-xs text-orange-600">Items need reordering • {businessMetrics.pendingOrders} pending orders</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-800">Business Health</CardTitle>
            <TrendingUp className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-900">{businessMetrics.profitMargin}%</div>
            <p className="text-xs text-purple-600">Profit margin • {businessMetrics.inventoryTurnover}x turnover rate</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales Trend */}
        <Card>
          <CardHeader>
            <CardTitle>Weekly Sales Performance</CardTitle>
            <CardDescription>Daily sales, orders, and customer metrics</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={salesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip formatter={(value, name) => [
                  name === 'sales' ? `$${value}` : value,
                  name === 'sales' ? 'Sales' : name === 'orders' ? 'Orders' : 'Customers'
                ]} />
                <Legend />
                <Area type="monotone" dataKey="sales" stackId="1" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.6} />
                <Area type="monotone" dataKey="orders" stackId="2" stroke="#10b981" fill="#10b981" fillOpacity={0.6} />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Product Category Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Sales by Category</CardTitle>
            <CardDescription>Product category performance breakdown</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={productCategoryData}
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}%`}
                >
                  {productCategoryData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Monthly Revenue Trend */}
      <Card>
        <CardHeader>
          <CardTitle>6-Month Business Trend</CardTitle>
          <CardDescription>Revenue, profit, and order volume over time</CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={350}>
            <LineChart data={monthlyTrend}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value, name) => [
                typeof value === 'number' && (name === 'revenue' || name === 'profit') ? `$${value.toLocaleString()}` : value,
                name === 'revenue' ? 'Revenue' : name === 'profit' ? 'Profit' : 'Orders'
              ]} />
              <Legend />
              <Line type="monotone" dataKey="revenue" stroke="#3b82f6" strokeWidth={3} />
              <Line type="monotone" dataKey="profit" stroke="#10b981" strokeWidth={2} />
              <Line type="monotone" dataKey="orders" stroke="#f59e0b" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Top Performing Products */}
        <Card>
          <CardHeader>
            <CardTitle>Top Products</CardTitle>
            <CardDescription>Best performing items this month</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topProducts.map((product, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <p className="font-medium text-sm">{product.name}</p>
                    <p className="text-xs text-gray-500">{product.sold} units • ${product.revenue.toLocaleString()}</p>
                  </div>
                  <Badge className={`text-xs ${product.trend.startsWith('+') ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {product.trend}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activities */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activities</CardTitle>
            <CardDescription>Latest business events and updates</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className="mt-0.5">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium">
                      {activity.customer || activity.supplier || activity.message}
                    </p>
                    <p className="text-xs text-gray-500">
                      {activity.product && `${activity.product} • `}
                      {activity.amount && `$${activity.amount} • `}
                      {activity.quantity && `${activity.quantity} units • `}
                      {activity.time}
                    </p>
                  </div>
                  <Badge className={`text-xs ${
                    activity.status === 'completed' ? 'bg-green-100 text-green-800' :
                    activity.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                    activity.status === 'warning' ? 'bg-orange-100 text-orange-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {activity.status}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Critical Alerts */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
                Critical Alerts
              </CardTitle>
              <CardDescription>Items requiring immediate attention</CardDescription>
            </div>
            <Button onClick={() => handleQuickAction('Auto Reorder')} className="bg-red-600 hover:bg-red-700">
              Auto Reorder
            </Button>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {lowStockAlerts.map((item, index) => (
                <div key={index} className={`flex items-center justify-between p-3 rounded-lg border-l-4 ${
                  item.severity === 'critical' ? 'bg-red-50 border-red-500' : 'bg-orange-50 border-orange-500'
                }`}>
                  <div>
                    <p className="font-medium text-sm">{item.name}</p>
                    <p className="text-xs text-gray-600">
                      Stock: {item.currentStock} | Min: {item.minStock}
                    </p>
                    <div className="mt-1">
                      <Progress 
                        value={(item.currentStock / item.minStock) * 100} 
                        className="h-2 w-24"
                      />
                    </div>
                  </div>
                  <div className="flex flex-col items-end">
                    <Battery className={`h-6 w-6 ${item.severity === 'critical' ? 'text-red-500' : 'text-orange-500'}`} />
                    <Badge className={`text-xs mt-1 ${
                      item.severity === 'critical' ? 'bg-red-100 text-red-800' : 'bg-orange-100 text-orange-800'
                    }`}>
                      {item.severity}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-4 grid grid-cols-2 gap-2">
              <Link to="/inventory">
                <Button variant="outline" className="w-full">
                  <Package className="h-4 w-4 mr-2" />
                  Manage Stock
                </Button>
              </Link>
              <Button onClick={() => handleQuickAction('Contact Suppliers')} variant="outline" className="w-full">
                <Truck className="h-4 w-4 mr-2" />
                Suppliers
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Action Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => handleQuickAction('Process Payments')}>
          <CardContent className="flex items-center justify-center p-6">
            <div className="text-center">
              <CreditCard className="h-8 w-8 mx-auto mb-2 text-blue-600" />
              <p className="font-medium">Process Payments</p>
              <p className="text-sm text-gray-500">3 pending</p>
            </div>
          </CardContent>
        </Card>

        <Link to="/customers">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="flex items-center justify-center p-6">
              <div className="text-center">
                <Users className="h-8 w-8 mx-auto mb-2 text-green-600" />
                <p className="font-medium">Customer Management</p>
                <p className="text-sm text-gray-500">{businessMetrics.totalCustomers} total</p>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link to="/reports">
          <Card className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="flex items-center justify-center p-6">
              <div className="text-center">
                <TrendingUp className="h-8 w-8 mx-auto mb-2 text-purple-600" />
                <p className="font-medium">Analytics</p>
                <p className="text-sm text-gray-500">View detailed reports</p>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Card className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => handleQuickAction('System Backup')}>
          <CardContent className="flex items-center justify-center p-6">
            <div className="text-center">
              <Zap className="h-8 w-8 mx-auto mb-2 text-orange-600" />
              <p className="font-medium">System Health</p>
              <p className="text-sm text-gray-500">All systems online</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
