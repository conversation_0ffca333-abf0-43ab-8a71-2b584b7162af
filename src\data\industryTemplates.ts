import { IndustryTemplate, BusinessTerminology } from '../types/business';

// Electronics Industry Template
const electronicsTerminology: BusinessTerminology = {
  product: { singular: 'Product', plural: 'Products' },
  inventory: { stock: 'Stock', warehouse: 'Warehouse', category: 'Category' },
  sales: { customer: 'Customer', order: 'Order', receipt: 'Receipt' },
  custom: {
    specification: 'Specification',
    model: 'Model',
    brand: 'Brand',
    warranty: 'Warranty'
  }
};

// Automotive Industry Template
const automotiveTerminology: BusinessTerminology = {
  product: { singular: 'Part', plural: 'Parts' },
  inventory: { stock: 'Inventory', warehouse: 'Warehouse', category: 'Category' },
  sales: { customer: 'Customer', order: 'Order', receipt: 'Invoice' },
  custom: {
    fitment: 'Vehicle Fitment',
    oem: 'OEM Number',
    brand: 'Brand',
    warranty: 'Warranty Period'
  }
};

// Clothing Industry Template
const clothingTerminology: BusinessTerminology = {
  product: { singular: 'Item', plural: 'Items' },
  inventory: { stock: 'Stock', warehouse: 'Store', category: 'Department' },
  sales: { customer: 'Customer', order: 'Sale', receipt: 'Receipt' },
  custom: {
    size: 'Size',
    color: 'Color',
    material: 'Material',
    season: 'Season'
  }
};

// Food & Beverage Industry Template
const foodBeverageTerminology: BusinessTerminology = {
  product: { singular: 'Product', plural: 'Products' },
  inventory: { stock: 'Inventory', warehouse: 'Storage', category: 'Category' },
  sales: { customer: 'Customer', order: 'Order', receipt: 'Receipt' },
  custom: {
    expiry: 'Expiry Date',
    batch: 'Batch Number',
    nutrition: 'Nutritional Info',
    allergens: 'Allergens'
  }
};

export const INDUSTRY_TEMPLATES: IndustryTemplate[] = [
  {
    id: 'electronics-consumer',
    name: 'Consumer Electronics',
    businessType: 'electronics',
    description: 'Smartphones, laptops, accessories, and consumer tech',
    terminology: electronicsTerminology,
    defaultCategories: [
      { name: 'Smartphones', color: 'bg-blue-500', sortOrder: 1 },
      { name: 'Laptops & Computers', color: 'bg-purple-500', sortOrder: 2 },
      { name: 'Audio & Video', color: 'bg-green-500', sortOrder: 3 },
      { name: 'Accessories', color: 'bg-orange-500', sortOrder: 4 },
      { name: 'Gaming', color: 'bg-red-500', sortOrder: 5 }
    ],
    defaultAttributes: [
      { name: 'brand', label: 'Brand', type: 'text', isRequired: true, sortOrder: 1 },
      { name: 'model', label: 'Model', type: 'text', isRequired: true, sortOrder: 2 },
      { name: 'color', label: 'Color', type: 'select', options: ['Black', 'White', 'Silver', 'Gold', 'Blue', 'Red'], isRequired: false, sortOrder: 3 },
      { name: 'warranty', label: 'Warranty (months)', type: 'number', isRequired: false, sortOrder: 4 },
      { name: 'specifications', label: 'Specifications', type: 'textarea', isRequired: false, sortOrder: 5 }
    ]
  },
  {
    id: 'automotive-parts',
    name: 'Automotive Parts',
    businessType: 'automotive',
    description: 'Auto parts, batteries, and automotive accessories',
    terminology: automotiveTerminology,
    defaultCategories: [
      { name: 'Batteries', color: 'bg-yellow-500', sortOrder: 1 },
      { name: 'Engine Parts', color: 'bg-red-500', sortOrder: 2 },
      { name: 'Brake System', color: 'bg-orange-500', sortOrder: 3 },
      { name: 'Electrical', color: 'bg-blue-500', sortOrder: 4 },
      { name: 'Filters', color: 'bg-green-500', sortOrder: 5 },
      { name: 'Accessories', color: 'bg-purple-500', sortOrder: 6 }
    ],
    defaultAttributes: [
      { name: 'brand', label: 'Brand', type: 'text', isRequired: true, sortOrder: 1 },
      { name: 'oem_number', label: 'OEM Number', type: 'text', isRequired: false, sortOrder: 2 },
      { name: 'vehicle_fitment', label: 'Vehicle Fitment', type: 'textarea', isRequired: false, sortOrder: 3 },
      { name: 'warranty_months', label: 'Warranty (months)', type: 'number', isRequired: false, sortOrder: 4 },
      { name: 'specifications', label: 'Specifications', type: 'textarea', isRequired: false, sortOrder: 5 }
    ]
  },
  {
    id: 'clothing-retail',
    name: 'Clothing Retail',
    businessType: 'clothing',
    description: 'Apparel, fashion accessories, and clothing items',
    terminology: clothingTerminology,
    defaultCategories: [
      { name: 'Men\'s Clothing', color: 'bg-blue-500', sortOrder: 1 },
      { name: 'Women\'s Clothing', color: 'bg-pink-500', sortOrder: 2 },
      { name: 'Children\'s Clothing', color: 'bg-yellow-500', sortOrder: 3 },
      { name: 'Shoes', color: 'bg-brown-500', sortOrder: 4 },
      { name: 'Accessories', color: 'bg-purple-500', sortOrder: 5 }
    ],
    defaultAttributes: [
      { name: 'size', label: 'Size', type: 'select', options: ['XS', 'S', 'M', 'L', 'XL', 'XXL'], isRequired: true, sortOrder: 1 },
      { name: 'color', label: 'Color', type: 'text', isRequired: true, sortOrder: 2 },
      { name: 'material', label: 'Material', type: 'text', isRequired: false, sortOrder: 3 },
      { name: 'brand', label: 'Brand', type: 'text', isRequired: false, sortOrder: 4 },
      { name: 'season', label: 'Season', type: 'select', options: ['Spring', 'Summer', 'Fall', 'Winter', 'All Season'], isRequired: false, sortOrder: 5 },
      { name: 'care_instructions', label: 'Care Instructions', type: 'textarea', isRequired: false, sortOrder: 6 }
    ]
  },
  {
    id: 'food-beverage-retail',
    name: 'Food & Beverage',
    businessType: 'food-beverage',
    description: 'Food products, beverages, and consumable goods',
    terminology: foodBeverageTerminology,
    defaultCategories: [
      { name: 'Fresh Produce', color: 'bg-green-500', sortOrder: 1 },
      { name: 'Beverages', color: 'bg-blue-500', sortOrder: 2 },
      { name: 'Packaged Foods', color: 'bg-orange-500', sortOrder: 3 },
      { name: 'Dairy Products', color: 'bg-yellow-500', sortOrder: 4 },
      { name: 'Frozen Foods', color: 'bg-cyan-500', sortOrder: 5 },
      { name: 'Snacks', color: 'bg-red-500', sortOrder: 6 }
    ],
    defaultAttributes: [
      { name: 'brand', label: 'Brand', type: 'text', isRequired: false, sortOrder: 1 },
      { name: 'expiry_date', label: 'Expiry Date', type: 'date', isRequired: true, sortOrder: 2 },
      { name: 'batch_number', label: 'Batch Number', type: 'text', isRequired: false, sortOrder: 3 },
      { name: 'weight', label: 'Weight/Volume', type: 'text', isRequired: false, sortOrder: 4 },
      { name: 'allergens', label: 'Allergens', type: 'multiselect', options: ['Nuts', 'Dairy', 'Gluten', 'Soy', 'Eggs', 'Fish', 'Shellfish'], isRequired: false, sortOrder: 5 },
      { name: 'nutritional_info', label: 'Nutritional Information', type: 'textarea', isRequired: false, sortOrder: 6 }
    ]
  },
  {
    id: 'general-retail',
    name: 'General Retail',
    businessType: 'general-retail',
    description: 'Mixed retail products and general merchandise',
    terminology: {
      product: { singular: 'Product', plural: 'Products' },
      inventory: { stock: 'Stock', warehouse: 'Warehouse', category: 'Category' },
      sales: { customer: 'Customer', order: 'Order', receipt: 'Receipt' },
      custom: {}
    },
    defaultCategories: [
      { name: 'Electronics', color: 'bg-blue-500', sortOrder: 1 },
      { name: 'Home & Garden', color: 'bg-green-500', sortOrder: 2 },
      { name: 'Clothing', color: 'bg-purple-500', sortOrder: 3 },
      { name: 'Sports & Outdoors', color: 'bg-orange-500', sortOrder: 4 },
      { name: 'Health & Beauty', color: 'bg-pink-500', sortOrder: 5 },
      { name: 'Miscellaneous', color: 'bg-gray-500', sortOrder: 6 }
    ],
    defaultAttributes: [
      { name: 'brand', label: 'Brand', type: 'text', isRequired: false, sortOrder: 1 },
      { name: 'color', label: 'Color', type: 'text', isRequired: false, sortOrder: 2 },
      { name: 'size', label: 'Size', type: 'text', isRequired: false, sortOrder: 3 },
      { name: 'weight', label: 'Weight', type: 'text', isRequired: false, sortOrder: 4 },
      { name: 'description', label: 'Description', type: 'textarea', isRequired: false, sortOrder: 5 }
    ]
  }
];
