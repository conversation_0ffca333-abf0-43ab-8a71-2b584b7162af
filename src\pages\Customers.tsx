import { useState } from "react";
import { Search, Plus, Edit, Trash2, Mail, Phone, MapPin, User, Building, DollarSign, Calendar, Eye, CreditCard, AlertTriangle } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import CustomerModal from "@/components/CustomerModal";
import CustomerCreditModal from "@/components/CustomerCreditModal";

interface Customer {
  id: number;
  name: string;
  email: string;
  phone: string;
  address: string;
  type: string;
  discount: number;
  notes: string;
  totalSpent: number;
  lastPurchase: string;
  status: string;
  creditLimit: number;
  currentBalance: number;
  paymentTerms: number;
  creditStatus: string;
  lastPayment: string;
}

const Customers = () => {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isCreditModalOpen, setIsCreditModalOpen] = useState(false);
  const [customers, setCustomers] = useState<Customer[]>([
    {
      id: 1,
      name: "John Smith",
      email: "<EMAIL>",
      phone: "(*************",
      address: "123 Main St, City, State 12345",
      type: "individual",
      discount: 5,
      notes: "Regular customer, prefers deep cycle batteries",
      totalSpent: 2450.50,
      lastPurchase: "2024-01-15",
      status: "active",
      creditLimit: 1000,
      currentBalance: 250,
      paymentTerms: 30,
      creditStatus: "good",
      lastPayment: "2024-01-10"
    },
    {
      id: 2,
      name: "ABC Auto Shop",
      email: "<EMAIL>",
      phone: "(*************",
      address: "456 Business Ave, City, State 12345",
      type: "business",
      discount: 15,
      notes: "Wholesale customer, bulk orders monthly",
      totalSpent: 15750.00,
      lastPurchase: "2024-01-18",
      status: "active",
      creditLimit: 5000,
      currentBalance: 1200,
      paymentTerms: 45,
      creditStatus: "good",
      lastPayment: "2024-01-05"
    },
    {
      id: 3,
      name: "Sarah Johnson",
      email: "<EMAIL>",
      phone: "(*************",
      address: "789 Oak St, City, State 12345",
      type: "individual",
      discount: 0,
      notes: "New customer",
      totalSpent: 129.99,
      lastPurchase: "2024-01-10",
      status: "active",
      creditLimit: 500,
      currentBalance: 0,
      paymentTerms: 15,
      creditStatus: "new",
      lastPayment: "2024-01-10"
    }
  ]);

  const filteredCustomers = customers.filter(customer =>
    customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    customer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    customer.phone.includes(searchQuery)
  );

  const handleAddCustomer = () => {
    setSelectedCustomer(null);
    setIsModalOpen(true);
  };

  const handleEditCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setIsModalOpen(true);
  };

  const handleManageCredit = (customer: Customer) => {
    setSelectedCustomer(customer);
    setIsCreditModalOpen(true);
  };

  const handleDeleteCustomer = (customerId: number) => {
    setCustomers(customers.filter(c => c.id !== customerId));
    toast({
      title: "Customer Deleted",
      description: "Customer has been removed from the system.",
    });
  };

  const handleSaveCustomer = (customerData: Customer) => {
    if (selectedCustomer) {
      setCustomers(customers.map(c => 
        c.id === selectedCustomer.id 
          ? { ...customerData, id: selectedCustomer.id, totalSpent: selectedCustomer.totalSpent, lastPurchase: selectedCustomer.lastPurchase, status: selectedCustomer.status }
          : c
      ));
    } else {
      const newCustomer = {
        ...customerData,
        id: Math.max(...customers.map(c => c.id)) + 1,
        totalSpent: 0,
        lastPurchase: new Date().toISOString().split('T')[0],
        status: "active",
        creditLimit: 500,
        currentBalance: 0,
        paymentTerms: 30,
        creditStatus: "new",
        lastPayment: new Date().toISOString().split('T')[0]
      };
      setCustomers([...customers, newCustomer]);
    }
  };

  const handleSaveCreditSettings = (creditData: Partial<Customer>) => {
    if (selectedCustomer) {
      setCustomers(customers.map(c => 
        c.id === selectedCustomer.id 
          ? { ...c, ...creditData }
          : c
      ));
      toast({
        title: "Credit Settings Updated",
        description: `Credit settings for ${selectedCustomer.name} have been updated.`,
      });
    }
  };

  const handleSendEmail = (customer: Customer) => {
    toast({
      title: "Email Sent",
      description: `Email sent to ${customer.name} at ${customer.email}`,
    });
  };

  const getCreditStatusColor = (status: string) => {
    switch (status) {
      case "good": return "default";
      case "warning": return "secondary";
      case "overdue": return "destructive";
      case "new": return "outline";
      default: return "outline";
    }
  };

  const getAgingDays = (lastPayment: string) => {
    const today = new Date();
    const paymentDate = new Date(lastPayment);
    const diffTime = Math.abs(today.getTime() - paymentDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const totalCustomers = customers.length;
  const activeCustomers = customers.filter(c => c.status === "active").length;
  const totalRevenue = customers.reduce((sum, c) => sum + c.totalSpent, 0);
  const averageSpend = totalRevenue / totalCustomers;
  const totalCreditUsed = customers.reduce((sum, c) => sum + c.currentBalance, 0);
  const overdueCustomers = customers.filter(c => c.creditStatus === "overdue").length;

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Customer Management</h1>
          <p className="text-gray-500">Manage your customer database and credit relationships</p>
        </div>
        <Button onClick={handleAddCustomer} className="bg-blue-600 hover:bg-blue-700">
          <Plus className="h-4 w-4 mr-2" />
          Add Customer
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCustomers}</div>
            <p className="text-xs text-muted-foreground">{activeCustomers} active</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">From all customers</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Spend</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${averageSpend.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">Per customer</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Credit Used</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalCreditUsed.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Total outstanding</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overdue</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overdueCustomers}</div>
            <p className="text-xs text-muted-foreground">Customers</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">+12</div>
            <p className="text-xs text-muted-foreground">New customers</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="customers" className="space-y-4">
        <TabsList>
          <TabsTrigger value="customers">All Customers</TabsTrigger>
          <TabsTrigger value="credit">Credit Management</TabsTrigger>
          <TabsTrigger value="aging">Aging Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="customers" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center space-x-4">
                <div className="relative flex-1">
                  <Search className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                  <Input
                    placeholder="Search customers by name, email, or phone..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Total Spent</TableHead>
                    <TableHead>Credit Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCustomers.map((customer) => (
                    <TableRow key={customer.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{customer.name}</p>
                          {customer.discount > 0 && (
                            <Badge variant="secondary" className="text-xs">
                              {customer.discount}% discount
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center text-sm">
                            <Mail className="h-3 w-3 mr-1" />
                            {customer.email}
                          </div>
                          <div className="flex items-center text-sm">
                            <Phone className="h-3 w-3 mr-1" />
                            {customer.phone}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={customer.type === "business" ? "default" : "outline"}>
                          {customer.type === "business" ? <Building className="h-3 w-3 mr-1" /> : <User className="h-3 w-3 mr-1" />}
                          {customer.type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className="font-medium">${customer.totalSpent.toLocaleString()}</span>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <Badge variant={getCreditStatusColor(customer.creditStatus)}>
                            {customer.creditStatus}
                          </Badge>
                          <div className="text-xs text-muted-foreground">
                            ${customer.currentBalance}/${customer.creditLimit}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleManageCredit(customer)}
                          >
                            <CreditCard className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleSendEmail(customer)}
                          >
                            <Mail className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditCustomer(customer)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteCustomer(customer.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="credit" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Credit Management Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer</TableHead>
                    <TableHead>Credit Limit</TableHead>
                    <TableHead>Current Balance</TableHead>
                    <TableHead>Available Credit</TableHead>
                    <TableHead>Payment Terms</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {customers.map((customer) => (
                    <TableRow key={customer.id}>
                      <TableCell className="font-medium">{customer.name}</TableCell>
                      <TableCell>${customer.creditLimit.toLocaleString()}</TableCell>
                      <TableCell>${customer.currentBalance.toLocaleString()}</TableCell>
                      <TableCell>${(customer.creditLimit - customer.currentBalance).toLocaleString()}</TableCell>
                      <TableCell>{customer.paymentTerms} days</TableCell>
                      <TableCell>
                        <Badge variant={getCreditStatusColor(customer.creditStatus)}>
                          {customer.creditStatus}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleManageCredit(customer)}
                        >
                          Manage
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="aging" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Accounts Receivable Aging</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer</TableHead>
                    <TableHead>Current Balance</TableHead>
                    <TableHead>Days Since Last Payment</TableHead>
                    <TableHead>Payment Terms</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {customers.filter(c => c.currentBalance > 0).map((customer) => {
                    const agingDays = getAgingDays(customer.lastPayment);
                    const isOverdue = agingDays > customer.paymentTerms;
                    
                    return (
                      <TableRow key={customer.id}>
                        <TableCell className="font-medium">{customer.name}</TableCell>
                        <TableCell>${customer.currentBalance.toLocaleString()}</TableCell>
                        <TableCell>{agingDays} days</TableCell>
                        <TableCell>{customer.paymentTerms} days</TableCell>
                        <TableCell>
                          <Badge variant={isOverdue ? "destructive" : "default"}>
                            {isOverdue ? "Overdue" : "Current"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleSendEmail(customer)}
                          >
                            Send Reminder
                          </Button>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <CustomerModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        customer={selectedCustomer}
        onSave={handleSaveCustomer}
      />

      <CustomerCreditModal
        isOpen={isCreditModalOpen}
        onClose={() => setIsCreditModalOpen(false)}
        customer={selectedCustomer}
        onSave={handleSaveCreditSettings}
      />
    </div>
  );
};

export default Customers;
