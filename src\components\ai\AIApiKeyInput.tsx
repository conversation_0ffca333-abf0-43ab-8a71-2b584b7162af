
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Key, Eye, EyeOff, ExternalLink } from "lucide-react";

interface AIApiKeyInputProps {
  onApiKeySet: (apiKey: string) => void;
}

export const AIApiKeyInput = ({ onApiKeySet }: AIApiKeyInputProps) => {
  const [apiKey, setApiKey] = useState("");
  const [showKey, setShowKey] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (apiKey.trim()) {
      onApiKeySet(apiKey.trim());
      localStorage.setItem('xai_api_key', apiKey.trim());
    }
  };

  return (
    <Card className="max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Key className="h-5 w-5 mr-2 text-blue-600" />
          AI Configuration Required
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <p className="text-sm text-gray-600">
            To enable AI features, please enter your xAI API key. You can get one from{" "}
            <a 
              href="https://x.ai/api" 
              target="_blank" 
              rel="noopener noreferrer"
              className="text-blue-600 hover:underline inline-flex items-center"
            >
              x.ai/api <ExternalLink className="h-3 w-3 ml-1" />
            </a>
          </p>
          
          <form onSubmit={handleSubmit} className="space-y-3">
            <div className="relative">
              <Input
                type={showKey ? "text" : "password"}
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="Enter your xAI API key"
                className="pr-10"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3"
                onClick={() => setShowKey(!showKey)}
              >
                {showKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </div>
            
            <Button type="submit" className="w-full" disabled={!apiKey.trim()}>
              Enable AI Features
            </Button>
          </form>
          
          <div className="text-xs text-gray-500 space-y-1">
            <p>• Your API key is stored locally in your browser</p>
            <p>• It's only used to communicate with xAI services</p>
            <p>• No data is sent to third parties</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
