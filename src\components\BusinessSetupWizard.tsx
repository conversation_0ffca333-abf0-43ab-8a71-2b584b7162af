import { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { BUSINESS_TYPES } from '@/types/business';
import { INDUSTRY_TEMPLATES } from '@/data/industryTemplates';
import { 
  Smartphone, Car, Shirt, Coffee, Heart, Home, 
  Dumbbell, Book, Gamepad2, Briefcase, Store 
} from 'lucide-react';

interface BusinessSetupWizardProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (businessName: string, templateId: string) => Promise<void>;
}

const iconMap = {
  Smartphone, Car, Shirt, Coffee, Heart, Home,
  Dumbbell, Book, Gamepad2, Briefcase, Store
};

export const BusinessSetupWizard = ({ isOpen, onClose, onComplete }: BusinessSetupWizardProps) => {
  const [step, setStep] = useState(1);
  const [businessName, setBusinessName] = useState('');
  const [selectedBusinessType, setSelectedBusinessType] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleNext = () => {
    if (step === 1 && !businessName.trim()) {
      toast({
        title: 'Business Name Required',
        description: 'Please enter your business name to continue.',
        variant: 'destructive'
      });
      return;
    }
    
    if (step === 2 && !selectedBusinessType) {
      toast({
        title: 'Business Type Required',
        description: 'Please select your business type to continue.',
        variant: 'destructive'
      });
      return;
    }
    
    setStep(step + 1);
  };

  const handleBack = () => {
    setStep(step - 1);
  };

  const handleComplete = async () => {
    if (!selectedTemplate) {
      toast({
        title: 'Template Required',
        description: 'Please select an industry template to continue.',
        variant: 'destructive'
      });
      return;
    }

    try {
      setIsLoading(true);
      await onComplete(businessName, selectedTemplate);
      
      toast({
        title: 'Setup Complete!',
        description: 'Your business has been configured successfully.',
      });
      
      onClose();
      resetWizard();
    } catch (error) {
      toast({
        title: 'Setup Failed',
        description: 'There was an error setting up your business. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const resetWizard = () => {
    setStep(1);
    setBusinessName('');
    setSelectedBusinessType('');
    setSelectedTemplate('');
  };

  const availableTemplates = INDUSTRY_TEMPLATES.filter(
    template => template.businessType === selectedBusinessType
  );

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl">Business Setup Wizard</DialogTitle>
        </DialogHeader>

        {/* Step 1: Business Name */}
        {step === 1 && (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">Welcome! Let's set up your business</h3>
              <p className="text-gray-600">First, tell us about your business</p>
            </div>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="businessName">Business Name</Label>
                <Input
                  id="businessName"
                  value={businessName}
                  onChange={(e) => setBusinessName(e.target.value)}
                  placeholder="Enter your business name"
                  className="text-lg"
                />
              </div>
            </div>

            <div className="flex justify-end">
              <Button onClick={handleNext} disabled={!businessName.trim()}>
                Next
              </Button>
            </div>
          </div>
        )}

        {/* Step 2: Business Type */}
        {step === 2 && (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">What type of business do you run?</h3>
              <p className="text-gray-600">Select the category that best describes your business</p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              {BUSINESS_TYPES.map((type) => {
                const IconComponent = iconMap[type.icon as keyof typeof iconMap];
                return (
                  <Card
                    key={type.id}
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      selectedBusinessType === type.id 
                        ? 'ring-2 ring-blue-500 bg-blue-50' 
                        : 'hover:bg-gray-50'
                    }`}
                    onClick={() => setSelectedBusinessType(type.id)}
                  >
                    <CardContent className="p-4 text-center">
                      <div className={`w-12 h-12 rounded-full ${type.color} flex items-center justify-center mx-auto mb-3`}>
                        {IconComponent && <IconComponent className="h-6 w-6 text-white" />}
                      </div>
                      <h4 className="font-semibold text-sm mb-1">{type.name}</h4>
                      <p className="text-xs text-gray-600">{type.description}</p>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            <div className="flex justify-between">
              <Button variant="outline" onClick={handleBack}>
                Back
              </Button>
              <Button onClick={handleNext} disabled={!selectedBusinessType}>
                Next
              </Button>
            </div>
          </div>
        )}

        {/* Step 3: Industry Template */}
        {step === 3 && (
          <div className="space-y-6">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">Choose your industry template</h3>
              <p className="text-gray-600">Select a template that matches your specific industry</p>
            </div>

            <div className="space-y-4">
              {availableTemplates.map((template) => (
                <Card
                  key={template.id}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedTemplate === template.id 
                      ? 'ring-2 ring-blue-500 bg-blue-50' 
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setSelectedTemplate(template.id)}
                >
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">{template.name}</CardTitle>
                        <CardDescription>{template.description}</CardDescription>
                      </div>
                      {selectedTemplate === template.id && (
                        <Badge variant="default">Selected</Badge>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div>
                        <h5 className="font-medium text-sm mb-2">Default Categories:</h5>
                        <div className="flex flex-wrap gap-1">
                          {template.defaultCategories.slice(0, 4).map((category) => (
                            <Badge key={category.name} variant="secondary" className="text-xs">
                              {category.name}
                            </Badge>
                          ))}
                          {template.defaultCategories.length > 4 && (
                            <Badge variant="secondary" className="text-xs">
                              +{template.defaultCategories.length - 4} more
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div>
                        <h5 className="font-medium text-sm mb-2">Product Attributes:</h5>
                        <div className="flex flex-wrap gap-1">
                          {template.defaultAttributes.slice(0, 3).map((attr) => (
                            <Badge key={attr.name} variant="outline" className="text-xs">
                              {attr.label}
                            </Badge>
                          ))}
                          {template.defaultAttributes.length > 3 && (
                            <Badge variant="outline" className="text-xs">
                              +{template.defaultAttributes.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <div className="flex justify-between">
              <Button variant="outline" onClick={handleBack}>
                Back
              </Button>
              <Button 
                onClick={handleComplete} 
                disabled={!selectedTemplate || isLoading}
              >
                {isLoading ? 'Setting up...' : 'Complete Setup'}
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
