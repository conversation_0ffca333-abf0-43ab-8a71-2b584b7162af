
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>alogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ArrowRightLeft } from "lucide-react";

interface StockTransferModalProps {
  isOpen: boolean;
  onClose: () => void;
  onTransfer: (data: any) => void;
  selectedItem: any;
}

const StockTransferModal = ({ isOpen, onClose, onTransfer, selectedItem }: StockTransferModalProps) => {
  const [transferData, setTransferData] = useState({
    fromLocation: "",
    toLocation: "",
    quantity: "",
    reason: ""
  });

  const locations = [
    { id: "A1-B2", name: "Warehouse A - Section 1" },
    { id: "B1-A3", name: "Warehouse B - Section 1" },
    { id: "C2-B1", name: "Warehouse C - Section 2" },
    { id: "D1-A2", name: "Warehouse D - Section 1" }
  ];

  const handleSubmit = () => {
    onTransfer({
      ...transferData,
      productId: selectedItem?.id,
      transferDate: new Date().toISOString()
    });
    onClose();
    setTransferData({
      fromLocation: "",
      toLocation: "",
      quantity: "",
      reason: ""
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ArrowRightLeft className="h-5 w-5" />
            Transfer Stock
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {selectedItem && (
            <div className="p-3 bg-gray-50 rounded-lg">
              <p className="font-medium">{selectedItem.name}</p>
              <p className="text-sm text-gray-500">Available: {selectedItem.currentStock} units</p>
            </div>
          )}

          <div>
            <Label htmlFor="fromLocation">From Location</Label>
            <Select value={transferData.fromLocation} onValueChange={(value) => setTransferData({...transferData, fromLocation: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Select source location" />
              </SelectTrigger>
              <SelectContent>
                {locations.map((location) => (
                  <SelectItem key={location.id} value={location.id}>
                    {location.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="toLocation">To Location</Label>
            <Select value={transferData.toLocation} onValueChange={(value) => setTransferData({...transferData, toLocation: value})}>
              <SelectTrigger>
                <SelectValue placeholder="Select destination location" />
              </SelectTrigger>
              <SelectContent>
                {locations.filter(loc => loc.id !== transferData.fromLocation).map((location) => (
                  <SelectItem key={location.id} value={location.id}>
                    {location.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="quantity">Quantity to Transfer</Label>
            <Input
              id="quantity"
              type="number"
              value={transferData.quantity}
              onChange={(e) => setTransferData({...transferData, quantity: e.target.value})}
              placeholder="Enter quantity"
              max={selectedItem?.currentStock}
            />
          </div>

          <div>
            <Label htmlFor="reason">Transfer Reason</Label>
            <Input
              id="reason"
              value={transferData.reason}
              onChange={(e) => setTransferData({...transferData, reason: e.target.value})}
              placeholder="Enter reason for transfer"
            />
          </div>

          <div className="flex gap-2 pt-4">
            <Button variant="outline" onClick={onClose} className="flex-1">
              Cancel
            </Button>
            <Button onClick={handleSubmit} className="flex-1">
              Transfer Stock
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default StockTransferModal;
