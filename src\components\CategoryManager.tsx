import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { useBusinessConfig } from '@/hooks/useBusinessConfig';
import { ProductCategory } from '@/types/business';
import { Plus, Edit, Trash2, Palette } from 'lucide-react';

interface CategoryManagerProps {
  trigger?: React.ReactNode;
}

const COLOR_OPTIONS = [
  'bg-red-500', 'bg-blue-500', 'bg-green-500', 'bg-yellow-500',
  'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-orange-500',
  'bg-cyan-500', 'bg-gray-500', 'bg-emerald-500', 'bg-rose-500'
];

export const CategoryManager = ({ trigger }: CategoryManagerProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isAddingCategory, setIsAddingCategory] = useState(false);
  const [editingCategory, setEditingCategory] = useState<ProductCategory | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    color: 'bg-blue-500'
  });

  const { categories, createCategory, config, terminology } = useBusinessConfig();
  const { toast } = useToast();

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      color: 'bg-blue-500'
    });
    setEditingCategory(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast({
        title: 'Name Required',
        description: `Please enter a ${terminology.inventory.category.toLowerCase()} name.`,
        variant: 'destructive'
      });
      return;
    }

    if (!config) {
      toast({
        title: 'Configuration Error',
        description: 'Business configuration not found.',
        variant: 'destructive'
      });
      return;
    }

    try {
      const categoryData = {
        businessId: config.id,
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        color: formData.color,
        sortOrder: categories.length + 1,
        isActive: true
      };

      await createCategory(categoryData);
      
      toast({
        title: `${terminology.inventory.category} Created`,
        description: `${formData.name} has been added successfully.`
      });

      resetForm();
      setIsAddingCategory(false);
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to create ${terminology.inventory.category.toLowerCase()}.`,
        variant: 'destructive'
      });
    }
  };

  const handleEdit = (category: ProductCategory) => {
    setFormData({
      name: category.name,
      description: category.description || '',
      color: category.color
    });
    setEditingCategory(category);
    setIsAddingCategory(true);
  };

  const handleCancel = () => {
    resetForm();
    setIsAddingCategory(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline">
            <Edit className="h-4 w-4 mr-2" />
            Manage {terminology.inventory.category}s
          </Button>
        )}
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5" />
            {terminology.inventory.category} Management
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Add Category Form */}
          {isAddingCategory && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">
                  {editingCategory ? 'Edit' : 'Add New'} {terminology.inventory.category}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">{terminology.inventory.category} Name *</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        placeholder={`Enter ${terminology.inventory.category.toLowerCase()} name`}
                      />
                    </div>
                    <div>
                      <Label htmlFor="color">Color</Label>
                      <div className="flex flex-wrap gap-2 mt-2">
                        {COLOR_OPTIONS.map((color) => (
                          <button
                            key={color}
                            type="button"
                            className={`w-8 h-8 rounded-full ${color} border-2 ${
                              formData.color === color ? 'border-gray-800' : 'border-gray-300'
                            }`}
                            onClick={() => setFormData(prev => ({ ...prev, color }))}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Optional description"
                      rows={2}
                    />
                  </div>

                  <div className="flex justify-end gap-2">
                    <Button type="button" variant="outline" onClick={handleCancel}>
                      Cancel
                    </Button>
                    <Button type="submit">
                      {editingCategory ? 'Update' : 'Create'} {terminology.inventory.category}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          )}

          {/* Categories List */}
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">
                Current {terminology.inventory.category}s ({categories.length})
              </h3>
              {!isAddingCategory && (
                <Button onClick={() => setIsAddingCategory(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add {terminology.inventory.category}
                </Button>
              )}
            </div>

            {categories.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Palette className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No {terminology.inventory.category.toLowerCase()}s created yet.</p>
                <p className="text-sm">Add your first {terminology.inventory.category.toLowerCase()} to get started.</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {categories.map((category) => (
                  <Card key={category.id} className="relative">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <div className={`w-4 h-4 rounded-full ${category.color}`} />
                          <h4 className="font-semibold">{category.name}</h4>
                        </div>
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEdit(category)}
                          >
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      
                      {category.description && (
                        <p className="text-sm text-gray-600 mb-2">{category.description}</p>
                      )}
                      
                      <Badge variant="secondary" className="text-xs">
                        Order: {category.sortOrder}
                      </Badge>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
