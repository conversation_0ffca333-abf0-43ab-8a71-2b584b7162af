
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger,
  DialogFooter
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  Recycle, 
  Plus, 
  Calculator, 
  Building2, 
  FileText, 
  DollarSign,
  Trash
} from "lucide-react";
import { toast } from "@/hooks/use-toast";

interface ReturnedItem {
  id: string;
  customerName: string;
  itemType: string;
  condition: string;
  refundValue: number;
  restockingFee: number;
  returnDate: string;
  status: 'received' | 'processed' | 'resold' | 'disposed';
  quantity: number;
  vendor?: string;
}

interface Vendor {
  id: string;
  name: string;
  contact: string;
  email: string;
  buybackRate: number;
  categories: string[];
  status: 'active' | 'inactive';
}

const ReturnsManagement = () => {
  const [returnedItems, setReturnedItems] = useState<ReturnedItem[]>([
    {
      id: "RET001",
      customerName: "John Smith",
      itemType: "Laptop Computer",
      condition: "Defective",
      refundValue: 850,
      restockingFee: 50,
      returnDate: "2024-07-01",
      status: "received",
      quantity: 1,
      vendor: "TechVendor Corp"
    },
    {
      id: "RET002", 
      customerName: "Mike Johnson",
      itemType: "Wireless Headphones",
      condition: "Damaged",
      refundValue: 120,
      restockingFee: 15,
      returnDate: "2024-07-02",
      status: "processed",
      quantity: 1
    }
  ]);

  const [vendors, setVendors] = useState<Vendor[]>([
    {
      id: "VND001",
      name: "TechVendor Corp",
      contact: "Sarah Wilson",
      email: "<EMAIL>",
      buybackRate: 0.65,
      categories: ["Electronics", "Computers"],
      status: "active"
    },
    {
      id: "VND002",
      name: "RetailReturn Solutions",
      contact: "David Chen",
      email: "<EMAIL>", 
      buybackRate: 0.58,
      categories: ["General Merchandise", "Clothing"],
      status: "active"
    }
  ]);

  const [isAddReturnOpen, setIsAddReturnOpen] = useState(false);
  const [isAddVendorOpen, setIsAddVendorOpen] = useState(false);
  const [isCalculatorOpen, setIsCalculatorOpen] = useState(false);

  const [newReturn, setNewReturn] = useState({
    customerName: "",
    itemType: "",
    condition: "",
    quantity: "",
    refundValue: "",
    restockingFee: ""
  });

  const [newVendor, setNewVendor] = useState({
    name: "",
    contact: "",
    email: "",
    buybackRate: "",
    categories: ""
  });

  const [calculator, setCalculator] = useState({
    itemType: "",
    condition: "",
    age: "",
    originalPrice: ""
  });

  const handleAddReturn = () => {
    const returnItem: ReturnedItem = {
      id: `RET${String(returnedItems.length + 1).padStart(3, '0')}`,
      customerName: newReturn.customerName,
      itemType: newReturn.itemType,
      condition: newReturn.condition,
      refundValue: parseFloat(newReturn.refundValue),
      restockingFee: parseFloat(newReturn.restockingFee),
      returnDate: new Date().toISOString().split('T')[0],
      status: 'received',
      quantity: parseInt(newReturn.quantity)
    };

    setReturnedItems([...returnedItems, returnItem]);
    setNewReturn({
      customerName: "",
      itemType: "",
      condition: "",
      quantity: "",
      refundValue: "",
      restockingFee: ""
    });
    setIsAddReturnOpen(false);
    toast({
      title: "Return Added",
      description: `Added ${returnItem.itemType} from ${returnItem.customerName}`
    });
  };

  const handleAddVendor = () => {
    const vendor: Vendor = {
      id: `VND${String(vendors.length + 1).padStart(3, '0')}`,
      name: newVendor.name,
      contact: newVendor.contact,
      email: newVendor.email,
      buybackRate: parseFloat(newVendor.buybackRate),
      categories: newVendor.categories.split(',').map(cat => cat.trim()),
      status: 'active'
    };

    setVendors([...vendors, vendor]);
    setNewVendor({
      name: "",
      contact: "",
      email: "",
      buybackRate: "",
      categories: ""
    });
    setIsAddVendorOpen(false);
    toast({
      title: "Vendor Added",
      description: `Added ${vendor.name} as vendor`
    });
  };

  const calculateRefundValue = () => {
    const originalPrice = parseFloat(calculator.originalPrice);
    if (!originalPrice) {
      toast({
        title: "Error",
        description: "Please enter the original price"
      });
      return;
    }
    
    // Adjust for condition
    let conditionMultiplier = 1;
    if (calculator.condition === "Like New") conditionMultiplier = 0.9;
    else if (calculator.condition === "Good") conditionMultiplier = 0.7;
    else if (calculator.condition === "Fair") conditionMultiplier = 0.5;
    else if (calculator.condition === "Poor") conditionMultiplier = 0.3;
    else conditionMultiplier = 0.1; // Defective

    // Adjust for age
    const age = parseInt(calculator.age);
    let ageMultiplier = 1;
    if (age <= 30) ageMultiplier = 1; // 30 days
    else if (age <= 90) ageMultiplier = 0.8; // 90 days
    else ageMultiplier = 0.6; // older

    const finalValue = Math.round(originalPrice * conditionMultiplier * ageMultiplier);
    
    toast({
      title: "Refund Value Calculated",
      description: `Estimated refund value: $${finalValue}`
    });
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      received: "bg-blue-100 text-blue-800",
      processed: "bg-yellow-100 text-yellow-800", 
      resold: "bg-green-100 text-green-800",
      disposed: "bg-red-100 text-red-800"
    };
    return <Badge className={variants[status as keyof typeof variants]}>{status}</Badge>;
  };

  const totalRefundValue = returnedItems.reduce((sum, item) => sum + item.refundValue, 0);
  const totalRestockingFees = returnedItems.reduce((sum, item) => sum + item.restockingFee, 0);
  const totalQuantity = returnedItems.reduce((sum, item) => sum + item.quantity, 0);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Returns Management</h1>
          <p className="text-gray-600">Manage product returns, vendor buybacks, and processing</p>
        </div>
        <div className="flex gap-2">
          <Dialog open={isCalculatorOpen} onOpenChange={setIsCalculatorOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Calculator className="h-4 w-4 mr-2" />
                Value Calculator
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Refund Value Calculator</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label>Item Type</Label>
                  <Input
                    value={calculator.itemType}
                    onChange={(e) => setCalculator({...calculator, itemType: e.target.value})}
                    placeholder="e.g., Laptop Computer"
                  />
                </div>
                <div>
                  <Label>Condition</Label>
                  <Input
                    value={calculator.condition}
                    onChange={(e) => setCalculator({...calculator, condition: e.target.value})}
                    placeholder="Like New, Good, Fair, Poor, Defective"
                  />
                </div>
                <div>
                  <Label>Age (days)</Label>
                  <Input
                    type="number"
                    value={calculator.age}
                    onChange={(e) => setCalculator({...calculator, age: e.target.value})}
                    placeholder="Days since purchase"
                  />
                </div>
                <div>
                  <Label>Original Price ($)</Label>
                  <Input
                    type="number"
                    value={calculator.originalPrice}
                    onChange={(e) => setCalculator({...calculator, originalPrice: e.target.value})}
                    placeholder="Original purchase price"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button onClick={calculateRefundValue}>Calculate Value</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
          
          <Dialog open={isAddReturnOpen} onOpenChange={setIsAddReturnOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Return
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Return</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label>Customer Name</Label>
                  <Input
                    value={newReturn.customerName}
                    onChange={(e) => setNewReturn({...newReturn, customerName: e.target.value})}
                    placeholder="Customer name"
                  />
                </div>
                <div>
                  <Label>Item Type</Label>
                  <Input
                    value={newReturn.itemType}
                    onChange={(e) => setNewReturn({...newReturn, itemType: e.target.value})}
                    placeholder="e.g., Laptop Computer"
                  />
                </div>
                <div>
                  <Label>Condition</Label>
                  <Input
                    value={newReturn.condition}
                    onChange={(e) => setNewReturn({...newReturn, condition: e.target.value})}
                    placeholder="Like New, Good, Fair, Poor, Defective"
                  />
                </div>
                <div>
                  <Label>Quantity</Label>
                  <Input
                    type="number"
                    value={newReturn.quantity}
                    onChange={(e) => setNewReturn({...newReturn, quantity: e.target.value})}
                    placeholder="Number of items"
                  />
                </div>
                <div>
                  <Label>Refund Value ($)</Label>
                  <Input
                    type="number"
                    value={newReturn.refundValue}
                    onChange={(e) => setNewReturn({...newReturn, refundValue: e.target.value})}
                    placeholder="Refund amount"
                  />
                </div>
                <div>
                  <Label>Restocking Fee ($)</Label>
                  <Input
                    type="number"
                    value={newReturn.restockingFee}
                    onChange={(e) => setNewReturn({...newReturn, restockingFee: e.target.value})}
                    placeholder="Restocking fee amount"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button onClick={handleAddReturn}>Add Return</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Refund Value</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalRefundValue}</div>
            <p className="text-xs text-muted-foreground">Refund values</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Restocking Fees</CardTitle>
            <DollarSign className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalRestockingFees}</div>
            <p className="text-xs text-muted-foreground">Processing fees</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            <Recycle className="h-4 w-4 text-gray-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalQuantity}</div>
            <p className="text-xs text-muted-foreground">Returned items</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Vendors</CardTitle>
            <Building2 className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{vendors.filter(v => v.status === 'active').length}</div>
            <p className="text-xs text-muted-foreground">Buyback vendors</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="returns-inventory" className="space-y-4">
        <TabsList>
          <TabsTrigger value="returns-inventory">Returns Inventory</TabsTrigger>
          <TabsTrigger value="vendors">Vendors</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="returns-inventory">
          <Card>
            <CardHeader>
              <CardTitle>Returns Inventory</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Item Type</TableHead>
                    <TableHead>Condition</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Refund Value</TableHead>
                    <TableHead>Restocking Fee</TableHead>
                    <TableHead>Return Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {returnedItems.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.id}</TableCell>
                      <TableCell>{item.customerName}</TableCell>
                      <TableCell>{item.itemType}</TableCell>
                      <TableCell>{item.condition}</TableCell>
                      <TableCell>{item.quantity}</TableCell>
                      <TableCell>${item.refundValue}</TableCell>
                      <TableCell>${item.restockingFee}</TableCell>
                      <TableCell>{item.returnDate}</TableCell>
                      <TableCell>{getStatusBadge(item.status)}</TableCell>
                      <TableCell>
                        <Button variant="outline" size="sm">
                          Process
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="vendors">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Vendors</CardTitle>
              <Dialog open={isAddVendorOpen} onOpenChange={setIsAddVendorOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Vendor
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Add Vendor</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label>Company Name</Label>
                      <Input
                        value={newVendor.name}
                        onChange={(e) => setNewVendor({...newVendor, name: e.target.value})}
                        placeholder="Vendor company name"
                      />
                    </div>
                    <div>
                      <Label>Contact Person</Label>
                      <Input
                        value={newVendor.contact}
                        onChange={(e) => setNewVendor({...newVendor, contact: e.target.value})}
                        placeholder="Contact person name"
                      />
                    </div>
                    <div>
                      <Label>Email</Label>
                      <Input
                        type="email"
                        value={newVendor.email}
                        onChange={(e) => setNewVendor({...newVendor, email: e.target.value})}
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div>
                      <Label>Buyback Rate (0-1)</Label>
                      <Input
                        type="number"
                        step="0.01"
                        value={newVendor.buybackRate}
                        onChange={(e) => setNewVendor({...newVendor, buybackRate: e.target.value})}
                        placeholder="0.65 (65% of original value)"
                      />
                    </div>
                    <div>
                      <Label>Categories (comma separated)</Label>
                      <Input
                        value={newVendor.categories}
                        onChange={(e) => setNewVendor({...newVendor, categories: e.target.value})}
                        placeholder="Electronics, Computers"
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button onClick={handleAddVendor}>Add Vendor</Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Company</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Buyback Rate</TableHead>
                    <TableHead>Categories</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {vendors.map((vendor) => (
                    <TableRow key={vendor.id}>
                      <TableCell className="font-medium">{vendor.name}</TableCell>
                      <TableCell>{vendor.contact}</TableCell>
                      <TableCell>{vendor.email}</TableCell>
                      <TableCell>{(vendor.buybackRate * 100).toFixed(0)}%</TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {vendor.categories.map((category, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {category}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={vendor.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                          {vendor.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button variant="outline" size="sm">
                          Edit
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Return Analytics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                  <span>Return Rate</span>
                  <Badge className="bg-green-100 text-green-800">2.3%</Badge>
                </div>
                <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
                  <span>Processing Time</span>
                  <Badge className="bg-blue-100 text-blue-800">24 hours</Badge>
                </div>
                <div className="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                  <span>Customer Satisfaction</span>
                  <Badge className="bg-yellow-100 text-yellow-800">4.2/5</Badge>
                </div>
                <Button className="w-full">
                  <FileText className="h-4 w-4 mr-2" />
                  Generate Analytics Report
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Recycle className="h-5 w-5" />
                  Processing Statistics
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>This Month</span>
                    <span className="font-medium">{totalQuantity} items</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-green-600 h-2 rounded-full" style={{width: '65%'}}></div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Year to Date</span>
                    <span className="font-medium">1,247 items</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-600 h-2 rounded-full" style={{width: '85%'}}></div>
                  </div>
                </div>
                <div className="pt-2 border-t">
                  <p className="text-sm text-gray-600">
                    Efficiency: 95% of returns processed within 24 hours
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ReturnsManagement;
